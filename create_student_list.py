#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从成绩数据创建学生名单
"""

import json

def create_student_list():
    """从grades.json创建学生名单"""
    try:
        # 读取成绩数据
        with open('grades.json', 'r', encoding='utf-8') as f:
            grades = json.load(f)
        
        # 提取学生信息
        students = []
        for name, data in grades.items():
            student = {
                'name': data['姓名'],
                'student_id': data['学号'],
                'class': data['班级']
            }
            students.append(student)
        
        # 保存学生名单
        with open('students.json', 'w', encoding='utf-8') as f:
            json.dump(students, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功创建学生名单文件，包含 {len(students)} 名学生")
        
        # 显示班级分布
        class_count = {}
        for student in students:
            class_name = student['class']
            class_count[class_name] = class_count.get(class_name, 0) + 1
        
        print(f"\n班级分布:")
        for class_name, count in sorted(class_count.items()):
            print(f"  {class_name}: {count}人")
        
    except Exception as e:
        print(f"❌ 创建学生名单失败: {e}")

if __name__ == "__main__":
    create_student_list()
