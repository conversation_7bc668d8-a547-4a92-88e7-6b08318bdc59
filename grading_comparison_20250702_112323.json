{"comparison_info": {"students_compared": 5, "comparison_time": "2025-07-02T11:23:23.106803", "methods": ["traditional_grading", "ai_grading"]}, "detailed_comparisons": [{"student_name": "童聪捷", "traditional": {"total_score": 70, "grade_level": "及格", "originality": 20, "completeness": 14, "standardization": 16, "data_analysis": 10, "insight_depth": 10}, "ai": {"total_score": 53, "grade_level": "不及格", "originality": 12, "completeness": 10, "standardization": 15, "data_analysis": 8, "insight_depth": 8}, "differences": {"total_score": -17, "originality": -8, "completeness": -4, "standardization": -1, "data_analysis": -2, "insight_depth": -2}}, {"student_name": "陈凌琦", "traditional": {"total_score": 70, "grade_level": "及格", "originality": 20, "completeness": 14, "standardization": 16, "data_analysis": 10, "insight_depth": 10}, "ai": {"total_score": 58, "grade_level": "不及格", "originality": 14, "completeness": 13, "standardization": 15, "data_analysis": 8, "insight_depth": 8}, "differences": {"total_score": -12, "originality": -6, "completeness": -1, "standardization": -1, "data_analysis": -2, "insight_depth": -2}}, {"student_name": "万宇峰", "traditional": {"total_score": 69, "grade_level": "及格", "originality": 20, "completeness": 14, "standardization": 16, "data_analysis": 10, "insight_depth": 10}, "ai": {"total_score": 60, "grade_level": "及格", "originality": 14, "completeness": 15, "standardization": 15, "data_analysis": 8, "insight_depth": 8}, "differences": {"total_score": -9, "originality": -6, "completeness": 1, "standardization": -1, "data_analysis": -2, "insight_depth": -2}}, {"student_name": "龚炫宇", "traditional": {"total_score": 70, "grade_level": "及格", "originality": 20, "completeness": 14, "standardization": 16, "data_analysis": 10, "insight_depth": 10}, "ai": {"total_score": 60, "grade_level": "及格", "originality": 14, "completeness": 15, "standardization": 15, "data_analysis": 8, "insight_depth": 8}, "differences": {"total_score": -10, "originality": -6, "completeness": 1, "standardization": -1, "data_analysis": -2, "insight_depth": -2}}, {"student_name": "叶子奕", "traditional": {"total_score": 70, "grade_level": "及格", "originality": 20, "completeness": 14, "standardization": 16, "data_analysis": 10, "insight_depth": 10}, "ai": {"total_score": 53, "grade_level": "不及格", "originality": 12, "completeness": 10, "standardization": 15, "data_analysis": 8, "insight_depth": 8}, "differences": {"total_score": -17, "originality": -8, "completeness": -4, "standardization": -1, "data_analysis": -2, "insight_depth": -2}}], "summary_statistics": {"total_score_differences": {"mean": -13.0, "std": 3.8078865529319543, "min": -17.0, "max": -9.0}, "grade_consistency": {"matches": 2, "total": 5, "percentage": 40.0}}}