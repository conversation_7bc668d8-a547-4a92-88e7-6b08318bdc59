#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek演示设置脚本
创建示例配置并演示使用方法
"""

import json
import os

def create_demo_config():
    """创建演示配置"""
    print("🔧 创建DeepSeek演示配置...")
    
    # 创建示例配置（请替换为您的真实API密钥）
    demo_config = {
        "api_key": "your_deepseek_api_key_here",  # 请替换为您的真实API密钥
        "base_url": "https://api.deepseek.com",
        "model": "deepseek-reasoner",
        "max_tokens": 4000,
        "temperature": 0.3,
        "timeout": 60
    }
    
    # 保存配置文件
    with open('deepseek_config.json', 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 演示配置已创建: deepseek_config.json")
    print("⚠️  请编辑配置文件，填入您的真实DeepSeek API密钥")
    
    return demo_config

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📖 DeepSeek使用示例")
    print(f"="*60)
    
    print(f"1. 配置API密钥:")
    print(f"   编辑 deepseek_config.json 文件")
    print(f"   将 'your_deepseek_api_key_here' 替换为您的真实API密钥")
    
    print(f"\n2. 测试单个报告分析:")
    print(f"   python deepseek_ai_analyzer.py \"万宇峰\" \"实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf\"")
    
    print(f"\n3. 批量分析（测试模式）:")
    print(f"   python batch_deepseek_analyzer.py")
    print(f"   选择 '1' 进行测试模式（分析前5名学生）")
    
    print(f"\n4. 批量分析（完整模式）:")
    print(f"   python batch_deepseek_analyzer.py")
    print(f"   选择 '2' 进行完整分析（所有学生）")

def check_prerequisites():
    """检查前置条件"""
    print(f"\n🔍 检查系统前置条件...")
    
    issues = []
    
    # 检查学生名单
    if not os.path.exists('students.json'):
        issues.append("❌ 缺少学生名单文件 students.json")
        print("   请先运行: python create_student_list.py")
    else:
        print("✅ 学生名单文件存在")
    
    # 检查文档读取器
    if not os.path.exists('ultimate_document_reader.py'):
        issues.append("❌ 缺少文档读取器 ultimate_document_reader.py")
    else:
        print("✅ 文档读取器存在")
    
    # 检查报告文件夹
    if not os.path.exists('实验0-1大模型质量我来测'):
        issues.append("❌ 缺少报告文件夹")
    else:
        print("✅ 报告文件夹存在")
    
    if issues:
        print(f"\n⚠️  发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 所有前置条件满足")
        return True

def create_api_key_template():
    """创建API密钥模板文件"""
    template_content = """
# DeepSeek API 密钥配置说明

## 获取API密钥
1. 访问 DeepSeek 官网: https://platform.deepseek.com/
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥

## 配置方法

### 方法1: 编辑配置文件（推荐）
编辑 deepseek_config.json 文件:
```json
{
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "base_url": "https://api.deepseek.com",
  "model": "deepseek-reasoner"
}
```

### 方法2: 设置环境变量
```bash
set DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
set DEEPSEEK_BASE_URL=https://api.deepseek.com
```

## 注意事项
- 请妥善保管您的API密钥
- 不要将API密钥提交到版本控制系统
- 定期检查API使用量和费用
"""
    
    with open('DeepSeek_API密钥配置.txt', 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print("✅ API密钥配置说明已创建: DeepSeek_API密钥配置.txt")

def main():
    print("🚀 DeepSeek R1 实验报告AI分析系统 - 演示设置")
    print("="*70)
    
    # 检查前置条件
    prerequisites_ok = check_prerequisites()
    
    # 创建演示配置
    config = create_demo_config()
    
    # 创建API密钥配置说明
    create_api_key_template()
    
    # 显示使用示例
    show_usage_examples()
    
    print(f"\n🎯 下一步操作:")
    print(f"1. 获取您的DeepSeek API密钥")
    print(f"2. 编辑 deepseek_config.json，填入真实API密钥")
    print(f"3. 运行测试: python deepseek_ai_analyzer.py \"万宇峰\" \"报告路径\"")
    
    if not prerequisites_ok:
        print(f"\n⚠️  请先解决前置条件问题，然后再使用DeepSeek分析功能")
    
    print(f"\n📚 详细使用说明请参考: DeepSeek_使用说明.md")

if __name__ == "__main__":
    main()
