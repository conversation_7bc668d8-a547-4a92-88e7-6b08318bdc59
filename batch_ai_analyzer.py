#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量AI报告分析系统
使用大语言模型对所有学生报告进行深度分析
"""

import json
import os
import subprocess
import sys
import time
from datetime import datetime
from advanced_ai_analyzer import AdvancedAIAnalyzer

class BatchAIAnalyzer:
    def __init__(self):
        self.analyzer = AdvancedAIAnalyzer()
        self.results = []
        self.report_folder = "实验0-1大模型质量我来测"
        
    def load_student_list(self):
        """加载学生名单"""
        try:
            with open('students.json', 'r', encoding='utf-8') as f:
                students = json.load(f)
            print(f"✅ 成功加载 {len(students)} 名学生信息")
            return students
        except FileNotFoundError:
            print("❌ 未找到学生名单文件 students.json")
            return []
        except Exception as e:
            print(f"❌ 加载学生名单失败: {e}")
            return []
    
    def find_student_report(self, student_name):
        """查找学生报告文件"""
        if not os.path.exists(self.report_folder):
            return None
        
        # 搜索包含学生姓名的文件
        for root, dirs, files in os.walk(self.report_folder):
            for file in files:
                if student_name in file and file.lower().endswith(('.pdf', '.docx', '.doc')):
                    return os.path.join(root, file)
        return None
    
    def analyze_all_reports(self):
        """批量分析所有报告"""
        print("🚀 开始批量AI分析...")
        print("="*80)
        
        students = self.load_student_list()
        if not students:
            return
        
        total_students = len(students)
        successful_analyses = 0
        failed_analyses = 0
        no_report_count = 0
        
        start_time = time.time()
        
        for i, student in enumerate(students, 1):
            student_name = student['name']
            print(f"\n[{i}/{total_students}] 分析学生: {student_name}")
            
            # 查找报告文件
            report_file = self.find_student_report(student_name)
            if not report_file:
                print(f"  ❌ 未找到 {student_name} 的报告文件")
                no_report_count += 1
                
                # 记录未提交的学生
                self.results.append({
                    'student_name': student_name,
                    'class': student['class'],
                    'status': 'no_report',
                    'scores': None,
                    'total_score': 0,
                    'grade_level': '未提交',
                    'analysis_time': datetime.now().isoformat()
                })
                continue
            
            print(f"  📄 找到报告: {os.path.basename(report_file)}")
            
            try:
                # 执行AI分析
                analysis_result = self.analyzer.analyze_report(student_name, report_file)
                
                if analysis_result:
                    # 添加班级信息
                    analysis_result['class'] = student['class']
                    analysis_result['status'] = 'analyzed'
                    
                    self.results.append(analysis_result)
                    successful_analyses += 1
                    
                    print(f"  ✅ 分析完成 - 总分: {analysis_result['total_score']}/100 ({analysis_result['grade_level']})")
                else:
                    print(f"  ❌ 分析失败")
                    failed_analyses += 1
                    
                    # 记录分析失败的学生
                    self.results.append({
                        'student_name': student_name,
                        'class': student['class'],
                        'status': 'analysis_failed',
                        'scores': None,
                        'total_score': -1,
                        'grade_level': '分析失败',
                        'analysis_time': datetime.now().isoformat()
                    })
                
            except Exception as e:
                print(f"  ❌ 分析异常: {e}")
                failed_analyses += 1
                
                self.results.append({
                    'student_name': student_name,
                    'class': student['class'],
                    'status': 'error',
                    'scores': None,
                    'total_score': -1,
                    'grade_level': '错误',
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                })
            
            # 显示进度
            elapsed_time = time.time() - start_time
            avg_time_per_student = elapsed_time / i
            remaining_students = total_students - i
            estimated_remaining_time = avg_time_per_student * remaining_students
            
            print(f"  ⏱️  已用时: {elapsed_time:.1f}s, 预计剩余: {estimated_remaining_time:.1f}s")
        
        # 显示总结
        total_time = time.time() - start_time
        print(f"\n{'='*80}")
        print(f"🎯 批量分析完成!")
        print(f"{'='*80}")
        print(f"总学生数: {total_students}")
        print(f"成功分析: {successful_analyses}")
        print(f"分析失败: {failed_analyses}")
        print(f"未提交报告: {no_report_count}")
        print(f"总用时: {total_time:.1f}秒")
        print(f"平均每个学生: {total_time/total_students:.1f}秒")
    
    def generate_analysis_report(self):
        """生成分析报告"""
        if not self.results:
            print("❌ 没有分析结果可生成报告")
            return
        
        print(f"\n📊 生成AI分析报告...")
        
        # 统计分析
        successful_results = [r for r in self.results if r['status'] == 'analyzed']
        
        if successful_results:
            scores = [r['total_score'] for r in successful_results]
            avg_score = sum(scores) / len(scores)
            max_score = max(scores)
            min_score = min(scores)
            
            # 等级分布
            grade_distribution = {}
            for result in successful_results:
                grade = result['grade_level']
                grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
            
            # 各维度平均分
            dimension_averages = {}
            for dim in ['originality', 'completeness', 'standardization', 'data_analysis', 'insight_depth']:
                dim_scores = [r['scores'][dim] for r in successful_results if r['scores']]
                if dim_scores:
                    dimension_averages[dim] = sum(dim_scores) / len(dim_scores)
        
        # 生成详细报告
        report = {
            'analysis_summary': {
                'total_students': len(self.results),
                'successful_analyses': len(successful_results),
                'failed_analyses': len([r for r in self.results if r['status'] in ['analysis_failed', 'error']]),
                'no_reports': len([r for r in self.results if r['status'] == 'no_report']),
                'analysis_time': datetime.now().isoformat()
            },
            'score_statistics': {
                'average_score': avg_score if successful_results else 0,
                'max_score': max_score if successful_results else 0,
                'min_score': min_score if successful_results else 0,
                'grade_distribution': grade_distribution if successful_results else {},
                'dimension_averages': dimension_averages if successful_results else {}
            },
            'detailed_results': self.results
        }
        
        # 保存详细报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"ai_batch_analysis_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 详细分析报告已保存: {report_file}")
        
        # 生成CSV格式的成绩单
        self.generate_csv_report(timestamp)
        
        # 显示统计信息
        self.display_statistics(report)
    
    def generate_csv_report(self, timestamp):
        """生成CSV格式的成绩报告"""
        import csv
        
        csv_file = f"ai_grades_report_{timestamp}.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '序号', '班级', '姓名', '状态', '原创性', '完整性', '规范性', 
                '数据分析', '洞察深度', '总分', '等级', '主要优点', '主要不足', '改进建议'
            ])
            
            # 写入数据
            for i, result in enumerate(self.results, 1):
                if result['status'] == 'analyzed' and result['scores']:
                    scores = result['scores']
                    analysis = result.get('detailed_analysis', {})
                    
                    strengths = '; '.join(analysis.get('strengths', [])[:2])
                    weaknesses = '; '.join(analysis.get('weaknesses', [])[:2])
                    suggestions = '; '.join(analysis.get('suggestions', [])[:2])
                    
                    writer.writerow([
                        i, result['class'], result['student_name'], '已分析',
                        scores['originality'], scores['completeness'], scores['standardization'],
                        scores['data_analysis'], scores['insight_depth'],
                        result['total_score'], result['grade_level'],
                        strengths, weaknesses, suggestions
                    ])
                else:
                    writer.writerow([
                        i, result['class'], result['student_name'], result['status'],
                        '', '', '', '', '', result['total_score'], result['grade_level'],
                        '', '', ''
                    ])
        
        print(f"✅ CSV成绩报告已保存: {csv_file}")
    
    def display_statistics(self, report):
        """显示统计信息"""
        stats = report['analysis_summary']
        scores = report['score_statistics']
        
        print(f"\n📈 AI分析统计报告")
        print(f"{'='*60}")
        
        print(f"📊 基本统计:")
        print(f"  总学生数: {stats['total_students']}")
        print(f"  成功分析: {stats['successful_analyses']} ({stats['successful_analyses']/stats['total_students']*100:.1f}%)")
        print(f"  分析失败: {stats['failed_analyses']}")
        print(f"  未提交: {stats['no_reports']}")
        
        if scores['average_score'] > 0:
            print(f"\n🎯 成绩统计:")
            print(f"  平均分: {scores['average_score']:.1f}/100")
            print(f"  最高分: {scores['max_score']}/100")
            print(f"  最低分: {scores['min_score']}/100")
            
            print(f"\n📊 等级分布:")
            for grade, count in scores['grade_distribution'].items():
                percentage = count / stats['successful_analyses'] * 100
                print(f"  {grade}: {count}人 ({percentage:.1f}%)")
            
            print(f"\n📈 各维度平均分:")
            dimension_names = {
                'originality': '原创性',
                'completeness': '完整性',
                'standardization': '规范性', 
                'data_analysis': '数据分析',
                'insight_depth': '洞察深度'
            }
            
            for dim, avg in scores['dimension_averages'].items():
                name = dimension_names.get(dim, dim)
                print(f"  {name}: {avg:.1f}/20")

def main():
    print("🤖 AI驱动的批量报告分析系统")
    print("="*80)
    
    batch_analyzer = BatchAIAnalyzer()
    
    # 执行批量分析
    batch_analyzer.analyze_all_reports()
    
    # 生成分析报告
    batch_analyzer.generate_analysis_report()
    
    print(f"\n🎉 AI批量分析完成！")

if __name__ == "__main__":
    main()
