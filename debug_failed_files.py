#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试文档读取失败的文件
分析失败原因并尝试修复
"""

import os
import json
import subprocess
from pathlib import Path

def analyze_failed_files():
    """分析读取失败的文件"""
    
    # 读取成绩数据
    with open('grades.json', 'r', encoding='utf-8') as f:
        grades = json.load(f)
    
    # 找出读取失败的学生
    failed_students = []
    for name, grade in grades.items():
        if grade['综合得分'] == -1:
            failed_students.append(name)
    
    print(f"发现 {len(failed_students)} 名学生的文档读取失败")
    print("正在分析失败原因...\n")
    
    report_folder = "实验0-1大模型质量我来测"
    
    for i, student_name in enumerate(failed_students, 1):
        print(f"[{i}/{len(failed_students)}] 分析学生: {student_name}")
        
        # 查找学生文件
        matching_files = []
        for root, dirs, files in os.walk(report_folder):
            for file in files:
                if student_name in file and file.lower().endswith(('.pdf', '.docx', '.doc')):
                    matching_files.append(os.path.join(root, file))
        
        if not matching_files:
            print(f"  ❌ 未找到文件")
            continue
        
        file_path = matching_files[0]
        print(f"  📁 文件: {os.path.basename(file_path)}")
        
        # 检查文件基本信息
        try:
            file_size = os.path.getsize(file_path)
            print(f"  📊 文件大小: {file_size:,} 字节")
            
            if file_size == 0:
                print(f"  ❌ 文件为空")
                continue
            elif file_size < 1024:
                print(f"  ⚠️  文件过小，可能损坏")
            
        except Exception as e:
            print(f"  ❌ 无法读取文件信息: {e}")
            continue
        
        # 尝试不同的读取方法
        print(f"  🔍 尝试读取...")
        
        # 方法1: 使用健壮读取器
        try:
            result = subprocess.run([
                'python', 'robust_document_reader.py', file_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
            
            if result.returncode == 0 and "文档内容:" in result.stdout:
                content_start = result.stdout.find("文档内容:")
                content_start = result.stdout.find("="*50, content_start) + 50
                content = result.stdout[content_start:].strip()
                
                if content and len(content) > 100:
                    print(f"  ✅ 健壮读取器成功，内容长度: {len(content)}")
                    continue
                else:
                    print(f"  ⚠️  健壮读取器返回内容过短: {len(content) if content else 0}")
            else:
                print(f"  ❌ 健壮读取器失败: {result.stderr[:100] if result.stderr else '无错误信息'}")
                
        except subprocess.TimeoutExpired:
            print(f"  ❌ 健壮读取器超时")
        except Exception as e:
            print(f"  ❌ 健壮读取器异常: {e}")
        
        # 方法2: 检查文件头
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)
                print(f"  🔍 文件头: {header.hex()}")
                
                # 检查是否为有效的PDF或DOCX文件
                if header.startswith(b'%PDF'):
                    print(f"  ✅ 有效的PDF文件")
                elif header.startswith(b'PK'):
                    print(f"  ✅ 可能是DOCX文件（ZIP格式）")
                else:
                    print(f"  ❌ 未知文件格式")
                    
        except Exception as e:
            print(f"  ❌ 无法读取文件头: {e}")
        
        print()

def try_alternative_readers():
    """尝试使用替代的读取方法"""
    
    print("=== 尝试安装和使用替代读取库 ===")
    
    # 尝试安装额外的库
    libraries_to_install = [
        'pymupdf',  # 另一个强大的PDF库
        'python-docx2txt',  # DOCX转文本
        'textract',  # 通用文档提取库
    ]
    
    for lib in libraries_to_install:
        try:
            print(f"尝试安装 {lib}...")
            result = subprocess.run(['pip', 'install', lib], 
                                  capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"  ✅ {lib} 安装成功")
            else:
                print(f"  ❌ {lib} 安装失败: {result.stderr[:100]}")
        except Exception as e:
            print(f"  ❌ {lib} 安装异常: {e}")

def main():
    print("=== 文档读取失败问题诊断 ===\n")
    
    # 分析失败文件
    analyze_failed_files()
    
    # 尝试安装替代库
    try_alternative_readers()

if __name__ == "__main__":
    main()
