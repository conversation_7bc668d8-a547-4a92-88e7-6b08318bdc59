#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API 配置管理
"""

import os
import json

class DeepSeekConfig:
    """DeepSeek API配置管理类"""
    
    def __init__(self):
        self.config_file = "deepseek_config.json"
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "api_key": "",
            "base_url": "https://api.deepseek.com",
            "model": "deepseek-reasoner",
            "max_tokens": 4000,
            "temperature": 0.3,
            "timeout": 60
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    default_config.update(saved_config)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
        
        # 从环境变量覆盖配置
        if os.getenv('DEEPSEEK_API_KEY'):
            default_config['api_key'] = os.getenv('DEEPSEEK_API_KEY')
        if os.getenv('DEEPSEEK_BASE_URL'):
            default_config['base_url'] = os.getenv('DEEPSEEK_BASE_URL')
        
        return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置已保存到 {self.config_file}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def setup_interactive(self):
        """交互式配置设置"""
        print("🔧 DeepSeek API 配置设置")
        print("="*50)
        
        # API密钥
        current_key = self.config.get('api_key', '')
        masked_key = f"{current_key[:8]}...{current_key[-4:]}" if len(current_key) > 12 else "未设置"
        
        print(f"当前API密钥: {masked_key}")
        new_key = input("请输入DeepSeek API密钥 (回车跳过): ").strip()
        if new_key:
            self.config['api_key'] = new_key
        
        # 基础URL
        print(f"当前API地址: {self.config.get('base_url', '')}")
        new_url = input("请输入API地址 (回车使用默认): ").strip()
        if new_url:
            self.config['base_url'] = new_url
        
        # 模型选择
        print(f"当前模型: {self.config.get('model', '')}")
        print("可选模型: deepseek-reasoner, deepseek-chat")
        new_model = input("请选择模型 (回车使用默认): ").strip()
        if new_model:
            self.config['model'] = new_model
        
        # 保存配置
        self.save_config()
        
        # 测试连接
        if self.config.get('api_key'):
            print("\n🧪 测试API连接...")
            self.test_connection()
    
    def test_connection(self):
        """测试API连接"""
        try:
            import requests
            
            headers = {
                'Authorization': f'Bearer {self.config["api_key"]}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': self.config['model'],
                'messages': [
                    {'role': 'user', 'content': '你好，请回复"连接成功"'}
                ],
                'max_tokens': 10
            }
            
            response = requests.post(
                f'{self.config["base_url"]}/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                reply = result['choices'][0]['message']['content']
                print(f"✅ API连接成功! 模型回复: {reply}")
                return True
            else:
                print(f"❌ API连接失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试异常: {e}")
            return False
    
    def get_config(self):
        """获取当前配置"""
        return self.config.copy()
    
    def is_configured(self):
        """检查是否已配置"""
        return bool(self.config.get('api_key'))

def main():
    """配置管理主程序"""
    config_manager = DeepSeekConfig()
    
    if not config_manager.is_configured():
        print("⚠️  DeepSeek API未配置，开始配置...")
        config_manager.setup_interactive()
    else:
        print("✅ DeepSeek API已配置")
        
        choice = input("是否重新配置? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            config_manager.setup_interactive()
        else:
            print("当前配置:")
            config = config_manager.get_config()
            masked_key = f"{config['api_key'][:8]}...{config['api_key'][-4:]}" if len(config['api_key']) > 12 else "未设置"
            print(f"  API密钥: {masked_key}")
            print(f"  API地址: {config['base_url']}")
            print(f"  模型: {config['model']}")

if __name__ == "__main__":
    main()
