#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek网页端API分析器
尝试模拟网页端的文件上传功能
"""

import json
import os
import sys
import requests
import base64
import time
from datetime import datetime

class DeepSeekWebAPIAnalyzer:
    def __init__(self, api_key=None):
        """初始化DeepSeek网页端API分析器"""
        config = self.load_config_file()
        self.api_key = api_key or config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
        
        # 尝试不同的API端点
        self.api_endpoints = [
            "https://api.deepseek.com/v1/chat/completions",  # 标准API
            "https://chat.deepseek.com/api/v0/chat/completions",  # 网页端可能的API
            "https://api.deepseek.com/v1/files/upload",  # 可能的文件上传端点
        ]
        
        if not self.api_key:
            print("⚠️  警告：未设置DeepSeek API密钥")

    def load_config_file(self):
        """加载配置文件"""
        try:
            if os.path.exists('deepseek_config.json'):
                with open('deepseek_config.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def encode_file_to_base64(self, file_path):
        """将文件编码为base64"""
        try:
            with open(file_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            print(f"❌ 文件编码失败: {e}")
            return None

    def get_file_mime_type(self, file_path):
        """获取文件MIME类型"""
        ext = os.path.splitext(file_path)[1].lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword'
        }
        return mime_types.get(ext, 'application/octet-stream')

    def try_method_1_multimodal_message(self, file_path):
        """方法1: 尝试多模态消息格式"""
        print("  🔄 尝试方法1: 多模态消息格式...")
        
        file_base64 = self.encode_file_to_base64(file_path)
        if not file_base64:
            return None
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-reasoner',
            'messages': [
                {
                    'role': 'user',
                    'content': [
                        {
                            'type': 'text',
                            'text': '请分析这个实验报告文档，按照实验0-1评分框架进行评估。'
                        },
                        {
                            'type': 'file',
                            'file_url': {
                                'data': file_base64,
                                'mime_type': self.get_file_mime_type(file_path)
                            }
                        }
                    ]
                }
            ]
        }
        
        try:
            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                print("  ✅ 方法1成功!")
                return response.json()
            else:
                print(f"  ❌ 方法1失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 方法1异常: {e}")
            return None

    def try_method_2_file_upload_endpoint(self, file_path):
        """方法2: 尝试专门的文件上传端点"""
        print("  🔄 尝试方法2: 文件上传端点...")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }
        
        try:
            with open(file_path, 'rb') as f:
                files = {
                    'file': (os.path.basename(file_path), f, self.get_file_mime_type(file_path))
                }
                
                data = {
                    'purpose': 'analysis',
                    'model': 'deepseek-reasoner'
                }
                
                response = requests.post(
                    'https://api.deepseek.com/v1/files',
                    headers=headers,
                    files=files,
                    data=data,
                    timeout=60
                )
                
                if response.status_code == 200:
                    print("  ✅ 方法2成功!")
                    return response.json()
                else:
                    print(f"  ❌ 方法2失败: {response.status_code}")
                    return None
                    
        except Exception as e:
            print(f"  ❌ 方法2异常: {e}")
            return None

    def try_method_3_web_api_format(self, file_path):
        """方法3: 尝试网页端API格式"""
        print("  🔄 尝试方法3: 网页端API格式...")
        
        file_base64 = self.encode_file_to_base64(file_path)
        if not file_base64:
            return None
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Origin': 'https://chat.deepseek.com',
            'Referer': 'https://chat.deepseek.com/'
        }
        
        data = {
            'model': 'deepseek-reasoner',
            'messages': [
                {
                    'role': 'user',
                    'content': '请分析这个实验报告文档',
                    'attachments': [
                        {
                            'type': 'file',
                            'name': os.path.basename(file_path),
                            'data': file_base64,
                            'mime_type': self.get_file_mime_type(file_path)
                        }
                    ]
                }
            ],
            'stream': False
        }
        
        try:
            response = requests.post(
                'https://chat.deepseek.com/api/v0/chat/completions',
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                print("  ✅ 方法3成功!")
                return response.json()
            else:
                print(f"  ❌ 方法3失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 方法3异常: {e}")
            return None

    def try_method_4_vision_api(self, file_path):
        """方法4: 尝试视觉API格式（如果是PDF转图片）"""
        print("  🔄 尝试方法4: 视觉API格式...")
        
        # 这个方法需要将PDF转换为图片，比较复杂
        # 暂时跳过
        print("  ⚠️  方法4需要PDF转图片，暂时跳过")
        return None

    def analyze_with_file_upload(self, student_name, file_path):
        """尝试多种文件上传方法"""
        print(f"\n{'='*70}")
        print(f"🔍 DeepSeek文件上传实验: {student_name}")
        print(f"{'='*70}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        file_size = os.path.getsize(file_path)
        print(f"📄 文件: {os.path.basename(file_path)} ({file_size:,} 字节)")
        
        # 尝试不同的方法
        methods = [
            self.try_method_1_multimodal_message,
            self.try_method_2_file_upload_endpoint,
            self.try_method_3_web_api_format,
            self.try_method_4_vision_api
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"\n🧪 测试方法 {i}:")
            try:
                result = method(file_path)
                if result:
                    print(f"🎉 找到可用的文件上传方法!")
                    return result
            except Exception as e:
                print(f"  ❌ 方法{i}异常: {e}")
        
        print(f"\n❌ 所有文件上传方法都失败了")
        print(f"💡 建议继续使用文本提取方案")
        return None

def main():
    if len(sys.argv) < 3:
        print("用法: python deepseek_web_api_analyzer.py <学生姓名> <文件路径>")
        sys.exit(1)
    
    student_name = sys.argv[1]
    file_path = sys.argv[2]
    
    analyzer = DeepSeekWebAPIAnalyzer()
    result = analyzer.analyze_with_file_upload(student_name, file_path)
    
    if result:
        print(f"\n🎉 文件上传成功!")
        print(f"返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"\n💡 建议使用备用方案:")
        print(f"   python deepseek_hybrid_analyzer.py \"{student_name}\" \"{file_path}\"")

if __name__ == "__main__":
    main()
