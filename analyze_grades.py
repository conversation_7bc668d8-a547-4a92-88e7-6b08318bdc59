#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成绩统计分析工具
"""

import pandas as pd

def analyze_grades():
    try:
        df = pd.read_csv('实验成绩汇总.csv', encoding='utf-8-sig')
        
        print('=== 实验成绩统计分析 ===')
        print(f'总评估学生数: {len(df)}')
        print(f'平均分: {df["综合得分"].mean():.1f}')
        print(f'最高分: {df["综合得分"].max()}')
        print(f'最低分: {df["综合得分"].min()}')
        print()
        
        print('=== 等级分布 ===')
        print(df['评分等级'].value_counts())
        print()
        
        print('=== 班级分布 ===')
        print(df['班级'].value_counts())
        print()
        
        print('=== 各维度平均分 ===')
        print(f'原创性: {df["原创性"].mean():.1f}')
        print(f'完整性: {df["完整性"].mean():.1f}')
        print(f'规范性: {df["规范性"].mean():.1f}')
        print(f'数据分析: {df["数据分析"].mean():.1f}')
        print(f'洞察深度: {df["洞察深度"].mean():.1f}')
        print()
        
        print('=== 分数段分布 ===')
        score_ranges = pd.cut(df['综合得分'], bins=[0, 60, 70, 80, 90, 100], 
                             labels=['不及格(<60)', '及格(60-69)', '中等(70-79)', '良好(80-89)', '优秀(90-100)'])
        print(score_ranges.value_counts().sort_index())
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    analyze_grades()
