#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成绩统计分析工具
"""

import pandas as pd

def analyze_grades():
    try:
        df = pd.read_csv('实验成绩汇总.csv', encoding='utf-8-sig')

        print('=== 实验成绩统计分析 ===')
        print(f'总学生数: {len(df)}')

        # 分离正常成绩和特殊情况
        normal_scores = df[df['综合得分'] > 0]
        failed_reads = df[df['综合得分'] == -1]
        no_submission = df[df['综合得分'] == 0]

        print(f'成功评估学生数: {len(normal_scores)}')
        print(f'读取失败学生数: {len(failed_reads)}')
        print(f'未提交学生数: {len(no_submission)}')
        print()

        if len(normal_scores) > 0:
            print('=== 正常评估学生成绩分析 ===')
            print(f'平均分: {normal_scores["综合得分"].mean():.1f}')
            print(f'最高分: {normal_scores["综合得分"].max()}')
            print(f'最低分: {normal_scores["综合得分"].min()}')
            print(f'标准差: {normal_scores["综合得分"].std():.1f}')
            print()

            print('=== 等级分布 ===')
            print(normal_scores['评分等级'].value_counts())
            print()

            print('=== 各维度平均分（正常评估学生）===')
            print(f'原创性: {normal_scores["原创性"].mean():.1f}/20')
            print(f'完整性: {normal_scores["完整性"].mean():.1f}/20')
            print(f'规范性: {normal_scores["规范性"].mean():.1f}/20')
            print(f'数据分析: {normal_scores["数据分析"].mean():.1f}/20')
            print(f'洞察深度: {normal_scores["洞察深度"].mean():.1f}/20')
            print()

            print('=== 分数段分布（正常评估学生）===')
            score_ranges = pd.cut(normal_scores['综合得分'],
                                bins=[0, 60, 70, 80, 90, 100],
                                labels=['不及格(<60)', '及格(60-69)', '中等(70-79)', '良好(80-89)', '优秀(90-100)'])
            print(score_ranges.value_counts().sort_index())
            print()

        print('=== 班级分布 ===')
        class_stats = df.groupby('班级').agg({
            '综合得分': ['count', lambda x: sum(x > 0), lambda x: sum(x == 0), lambda x: sum(x == -1)]
        }).round(1)
        class_stats.columns = ['总人数', '成功评估', '未提交', '读取失败']
        print(class_stats)
        print()

        print('=== 特殊情况学生名单 ===')
        if len(no_submission) > 0:
            print(f'未提交报告学生({len(no_submission)}人):')
            for _, student in no_submission.iterrows():
                print(f'  {student["班级"]} - {student["姓名"]}')
            print()

        if len(failed_reads) > 0:
            print(f'文档读取失败学生({len(failed_reads)}人):')
            for _, student in failed_reads.iterrows():
                print(f'  {student["班级"]} - {student["姓名"]}')
            print()

        print('=== 评估完成率 ===')
        success_rate = len(normal_scores) / len(df) * 100
        print(f'成功评估率: {success_rate:.1f}%')
        print(f'问题学生比例: {(len(failed_reads) + len(no_submission)) / len(df) * 100:.1f}%')

    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    analyze_grades()
