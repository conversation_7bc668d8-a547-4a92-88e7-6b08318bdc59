#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek R1 驱动的实验报告分析器
使用DeepSeek R1 API进行深度报告分析
"""

import json
import os
import subprocess
import sys
import requests
import time
from datetime import datetime

class DeepSeekAIAnalyzer:
    def __init__(self, api_key=None, base_url=None):
        """
        初始化DeepSeek分析器

        Args:
            api_key: DeepSeek API密钥
            base_url: DeepSeek API基础URL，默认为官方地址
        """
        # 首先尝试从配置文件加载
        config = self.load_config_file()

        self.api_key = api_key or config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
        self.base_url = base_url or config.get('base_url') or os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')

        if not self.api_key:
            print("⚠️  警告：未设置DeepSeek API密钥")
            print("请设置环境变量 DEEPSEEK_API_KEY 或在初始化时传入api_key参数")

        # DeepSeek专用的分析提示词 - 基于实验0-1评分框架
        self.analysis_prompt_template = """
你是一位资深的计算机科学教授，拥有20年的教学经验，专门评估学生的实验报告。请严格按照以下评分框架对实验报告进行评估。

实验背景：
- 课程：大数据与人工智能
- 实验题目：大语言模型质量我来测
- 要求：学生需要测试不同的大语言模型，比较其性能，撰写分析报告

## 评分框架（总分100分，每维度20分）

### 1. 原创性（20分）- 自主完成程度与AI生成内容比例
- 优秀(18-20分)：人工分析痕迹明显，AI辅助内容<30%
- 良好(15-17分)：关键分析自主完成，AI辅助30%-50%
- 中等(12-14分)：框架自主搭建，核心内容依赖AI
- 及格(9-11分)：直接使用AI生成内容>70%
- 不及格(0-8分)：全文AI生成无修改痕迹

### 2. 完整性（20分）- 实验要求内容的完整覆盖度
必含要素检查：
- 3个模型测试记录（缺失扣4分/个）
- 12个提示词执行结果（缺失扣1分/个）
- 4维度评分表（缺失扣5分）
- 综合质量分值计算（缺失扣3分）
- 结论与建议（缺失扣5分）

### 3. 规范性（20分）- 报告表述的清晰度与规范程度
扣分规则：
- 错别字/病句（-0.5分/处，上限-5分）
- 逻辑矛盾（-2分/处）
- 图表无标题/单位（-3分/项）
- 报告结构缺失（-5分/部分）

### 4. 数据分析（20分）- 数据处理与模型比较深度
- 优秀(18-20分)：含对比图表+数据交叉分析
- 良好(15-17分)：有数据汇总但缺乏对比
- 中等(12-14分)：仅罗列原始数据
- 及格(9-11分)：数据记录不全
- 不及格(0-8分)：无有效数据

### 5. 洞察深度（20分）- 问题发现与改进建议价值
- 优秀(18-20分)：指出模型特定场景缺陷+可操作优化方案
- 良好(15-17分)：准确归纳各模型优缺点
- 中等(12-14分)：仅描述表面现象
- 及格(9-11分)：结论与数据脱节
- 不及格(0-8分)：无有效结论

## 评分等级转换
- 90-100分：优秀
- 80-89分：良好
- 70-79分：中等
- 60-69分：及格
- 0-59分：不及格

请仔细阅读以下学生报告内容，严格按照上述评分框架进行评估：

报告内容：
{report_content}

请严格按照以下JSON格式返回分析结果，确保JSON格式正确：

{{
    "scores": {{
        "originality": 分数(0-20的整数),
        "completeness": 分数(0-20的整数),
        "standardization": 分数(0-20的整数),
        "data_analysis": 分数(0-20的整数),
        "insight_depth": 分数(0-20的整数)
    }},
    "total_score": 总分(0-100的整数),
    "grade_level": "优秀/良好/中等/及格/不及格",
    "detailed_analysis": {{
        "strengths": ["具体优点1", "具体优点2", "具体优点3"],
        "weaknesses": ["具体不足1", "具体不足2", "具体不足3"],
        "suggestions": ["具体改进建议1", "具体改进建议2", "具体改进建议3"]
    }},
    "dimension_feedback": {{
        "originality": "原创性评价：具体分析AI辅助比例和自主分析痕迹",
        "completeness": "完整性评价：检查必含要素完成情况，明确扣分项",
        "standardization": "规范性评价：指出具体的格式、语言、逻辑问题",
        "data_analysis": "数据分析评价：评估图表质量和数据对比深度",
        "insight_depth": "洞察深度评价：分析结论质量和改进建议价值"
    }},
    "overall_comment": "总体评价（200-300字）",
    "scoring_evidence": {{
        "originality_evidence": "原创性评分依据：具体指出AI生成痕迹或自主分析证据",
        "completeness_evidence": "完整性评分依据：列出缺失的必含要素",
        "standardization_evidence": "规范性评分依据：具体的扣分项和位置",
        "data_analysis_evidence": "数据分析评分依据：图表和数据处理质量评估",
        "insight_depth_evidence": "洞察深度评分依据：结论质量和建议可操作性分析"
    }},
    "improvement_areas": ["需要改进的具体方面1", "需要改进的具体方面2"]
}}

评分要求：
1. 严格按照实验0-1评分框架执行，不得偏离标准
2. 每个维度必须提供具体的评分证据和扣分理由
3. 完整性评分必须检查所有必含要素
4. 规范性评分必须统计具体的错误数量
5. 确保返回的是有效的JSON格式
"""

    def load_config_file(self):
        """加载配置文件"""
        try:
            if os.path.exists('deepseek_config.json'):
                with open('deepseek_config.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
        return {}

    def call_deepseek_api(self, prompt, model="deepseek-reasoner", max_tokens=4000):
        """
        调用DeepSeek API进行分析
        
        Args:
            prompt: 分析提示词
            model: 使用的模型，默认为deepseek-reasoner
            max_tokens: 最大token数
            
        Returns:
            API响应内容或None
        """
        if not self.api_key:
            print("❌ DeepSeek API密钥未设置")
            return None
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': model,
                'messages': [
                    {
                        'role': 'system', 
                        'content': '你是一位专业的计算机科学教授，擅长评估学生实验报告。请严格按照要求的JSON格式返回分析结果。'
                    },
                    {
                        'role': 'user', 
                        'content': prompt
                    }
                ],
                'temperature': 0.3,  # 较低的温度确保结果稳定
                'max_tokens': max_tokens,
                'stream': False
            }
            
            print(f"  🚀 调用DeepSeek API (模型: {model})...")
            
            response = requests.post(
                f'{self.base_url}/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60  # 60秒超时
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 显示token使用情况
                if 'usage' in result:
                    usage = result['usage']
                    print(f"  📊 Token使用: 输入{usage.get('prompt_tokens', 0)}, "
                          f"输出{usage.get('completion_tokens', 0)}, "
                          f"总计{usage.get('total_tokens', 0)}")
                
                return content
            else:
                print(f"  ❌ DeepSeek API错误: {response.status_code}")
                print(f"  错误信息: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ❌ DeepSeek API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            print(f"  ❌ DeepSeek API网络错误: {e}")
            return None
        except Exception as e:
            print(f"  ❌ DeepSeek API调用异常: {e}")
            return None

    def parse_deepseek_response(self, response_text):
        """
        解析DeepSeek API响应
        
        Args:
            response_text: API返回的文本
            
        Returns:
            解析后的分析结果字典或None
        """
        try:
            # 尝试直接解析JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # 查找JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                return json.loads(json_str)
            
            print("  ❌ 响应中未找到有效的JSON格式")
            return None
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            print(f"  响应内容: {response_text[:500]}...")
            return None

    def validate_analysis_result(self, result):
        """验证分析结果的完整性"""
        if not isinstance(result, dict):
            return False
        
        required_keys = ['scores', 'total_score', 'grade_level', 'detailed_analysis', 
                        'dimension_feedback', 'overall_comment']
        
        if not all(key in result for key in required_keys):
            print(f"  ❌ 缺少必要字段: {[k for k in required_keys if k not in result]}")
            return False
        
        # 验证scores字段
        required_dimensions = ['originality', 'completeness', 'standardization', 
                             'data_analysis', 'insight_depth']
        
        if not all(dim in result['scores'] for dim in required_dimensions):
            print(f"  ❌ scores字段不完整")
            return False
        
        # 验证分数范围
        for dim, score in result['scores'].items():
            if not isinstance(score, (int, float)) or not (0 <= score <= 20):
                print(f"  ❌ {dim}分数无效: {score}")
                return False
        
        total_score = result.get('total_score', 0)
        if not isinstance(total_score, (int, float)) or not (0 <= total_score <= 100):
            print(f"  ❌ 总分无效: {total_score}")
            return False
        
        return True

    def extract_document_content(self, file_path):
        """提取文档内容"""
        try:
            result = subprocess.run([
                'python', 'ultimate_document_reader.py', file_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=60)
            
            if result.returncode == 0:
                try:
                    output_data = json.loads(result.stdout)
                    if output_data.get('success'):
                        return output_data.get('content')
                    else:
                        print(f"  ❌ 文档提取失败: {output_data.get('status', '未知错误')}")
                        return None
                except json.JSONDecodeError:
                    print(f"  ❌ 文档提取器返回格式错误")
                    return None
            else:
                print(f"  ❌ 文档提取失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"  ❌ 文档提取异常: {e}")
            return None

    def analyze_report_with_deepseek(self, student_name, file_path):
        """使用DeepSeek分析单个报告"""
        print(f"\n{'='*70}")
        print(f"🤖 DeepSeek R1 深度分析: {student_name}")
        print(f"{'='*70}")
        
        # 步骤1: 提取文档内容
        print(f"📄 步骤1: 提取报告内容...")
        content = self.extract_document_content(file_path)
        if not content:
            print(f"❌ 无法提取报告内容")
            return None
        
        print(f"✅ 成功提取报告内容 ({len(content)} 字符, {len(content.split())} 词)")
        
        # 步骤2: 准备分析提示词
        print(f"🔍 步骤2: 准备AI分析...")
        # 限制内容长度以避免token超限
        content_for_analysis = content[:8000] if len(content) > 8000 else content
        prompt = self.analysis_prompt_template.format(report_content=content_for_analysis)
        
        # 步骤3: 调用DeepSeek API
        print(f"🚀 步骤3: DeepSeek R1 分析中...")
        api_response = self.call_deepseek_api(prompt)
        
        if not api_response:
            print(f"❌ DeepSeek API调用失败")
            return None
        
        # 步骤4: 解析响应
        print(f"📊 步骤4: 解析分析结果...")
        analysis_result = self.parse_deepseek_response(api_response)
        
        if not analysis_result:
            print(f"❌ 无法解析DeepSeek响应")
            return None
        
        # 步骤5: 验证结果
        if not self.validate_analysis_result(analysis_result):
            print(f"❌ 分析结果格式不正确")
            return None
        
        # 添加元数据
        analysis_result.update({
            'student_name': student_name,
            'file_path': file_path,
            'analysis_time': datetime.now().isoformat(),
            'content_length': len(content),
            'word_count': len(content.split()),
            'ai_model': 'deepseek-reasoner',
            'api_provider': 'DeepSeek'
        })
        
        print(f"✅ DeepSeek分析完成!")
        return analysis_result

    def display_analysis_result(self, result):
        """显示分析结果"""
        if not result:
            return
        
        print(f"\n{'='*70}")
        print(f"📊 {result['student_name']} 的DeepSeek分析报告")
        print(f"{'='*70}")
        
        print(f"🎯 总分: {result['total_score']}/100 ({result['grade_level']})")
        print(f"📄 报告长度: {result['word_count']} 词")
        print(f"🤖 AI模型: {result['ai_model']}")
        
        print(f"\n📈 各维度得分:")
        dimension_names = {
            'originality': '原创性',
            'completeness': '完整性', 
            'standardization': '规范性',
            'data_analysis': '数据分析',
            'insight_depth': '洞察深度'
        }
        
        for dim, score in result['scores'].items():
            name = dimension_names.get(dim, dim)
            bar = '█' * (score // 2) + '░' * (10 - score // 2)
            print(f"  {name:8}: {score:2d}/20 [{bar}]")
        
        print(f"\n💬 总体评价:")
        print(f"  {result['overall_comment']}")
        
        print(f"\n✨ 主要优点:")
        for i, strength in enumerate(result['detailed_analysis']['strengths'], 1):
            print(f"  {i}. {strength}")
        
        print(f"\n⚠️  需要改进:")
        for i, weakness in enumerate(result['detailed_analysis']['weaknesses'], 1):
            print(f"  {i}. {weakness}")
        
        print(f"\n💡 改进建议:")
        for i, suggestion in enumerate(result['detailed_analysis']['suggestions'], 1):
            print(f"  {i}. {suggestion}")
        
        if result.get('highlight_quotes'):
            print(f"\n🌟 报告亮点:")
            for i, quote in enumerate(result['highlight_quotes'], 1):
                print(f"  {i}. \"{quote}\"")

def main():
    if len(sys.argv) < 3:
        print("用法: python deepseek_ai_analyzer.py <学生姓名> <文件路径> [API密钥]")
        print("示例: python deepseek_ai_analyzer.py '万宇峰' '实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf'")
        print("\n环境变量设置:")
        print("  set DEEPSEEK_API_KEY=your_api_key_here")
        print("  set DEEPSEEK_BASE_URL=https://api.deepseek.com  (可选)")
        sys.exit(1)
    
    student_name = sys.argv[1]
    file_path = sys.argv[2]
    api_key = sys.argv[3] if len(sys.argv) > 3 else None
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    # 创建分析器
    analyzer = DeepSeekAIAnalyzer(api_key=api_key)
    
    # 分析报告
    result = analyzer.analyze_report_with_deepseek(student_name, file_path)
    
    if result:
        # 显示结果
        analyzer.display_analysis_result(result)
        
        # 保存详细分析
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"deepseek_analysis_{student_name}_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细分析已保存: {output_file}")
        
    else:
        print(f"❌ 分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
