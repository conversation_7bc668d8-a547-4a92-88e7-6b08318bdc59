#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek R1 驱动的实验报告分析器
使用DeepSeek R1 API进行深度报告分析
"""

import json
import os
import subprocess
import sys
import requests
import time
from datetime import datetime

class DeepSeekAIAnalyzer:
    def __init__(self, api_key=None, base_url=None):
        """
        初始化DeepSeek分析器

        Args:
            api_key: DeepSeek API密钥
            base_url: DeepSeek API基础URL，默认为官方地址
        """
        # 首先尝试从配置文件加载
        config = self.load_config_file()

        self.api_key = api_key or config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
        self.base_url = base_url or config.get('base_url') or os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')

        if not self.api_key:
            print("⚠️  警告：未设置DeepSeek API密钥")
            print("请设置环境变量 DEEPSEEK_API_KEY 或在初始化时传入api_key参数")

        # DeepSeek专用的分析提示词
        self.analysis_prompt_template = """
你是一位资深的计算机科学教授，拥有20年的教学经验，专门评估学生的实验报告。请用你的专业知识对以下实验报告进行深度分析。

实验背景：
- 课程：大数据与人工智能
- 实验题目：大语言模型质量我来测
- 要求：学生需要测试不同的大语言模型，比较其性能，撰写分析报告

评估标准（每项20分，总分100分）：
1. 原创性(Originality)：内容的独创性、个人见解的深度、创新思维体现
2. 完整性(Completeness)：实验步骤完整性、数据充分性、结构完整性
3. 规范性(Standardization)：格式规范、引用标准、语言表达、学术写作规范
4. 数据分析(Data Analysis)：数据处理科学性、分析方法合理性、统计分析质量
5. 洞察深度(Insight Depth)：结论深刻性、问题理解程度、批判性思维

请仔细阅读以下学生报告内容，进行专业、客观、详细的评估：

报告内容：
{report_content}

请严格按照以下JSON格式返回分析结果，确保JSON格式正确：

{{
    "scores": {{
        "originality": 分数(0-20的整数),
        "completeness": 分数(0-20的整数),
        "standardization": 分数(0-20的整数),
        "data_analysis": 分数(0-20的整数),
        "insight_depth": 分数(0-20的整数)
    }},
    "total_score": 总分(0-100的整数),
    "grade_level": "优秀(90-100)/良好(80-89)/中等(70-79)/及格(60-69)/不及格(<60)",
    "detailed_analysis": {{
        "strengths": ["具体优点1", "具体优点2", "具体优点3"],
        "weaknesses": ["具体不足1", "具体不足2", "具体不足3"],
        "suggestions": ["具体改进建议1", "具体改进建议2", "具体改进建议3"]
    }},
    "dimension_feedback": {{
        "originality": "原创性详细评价（100-150字）",
        "completeness": "完整性详细评价（100-150字）",
        "standardization": "规范性详细评价（100-150字）",
        "data_analysis": "数据分析详细评价（100-150字）",
        "insight_depth": "洞察深度详细评价（100-150字）"
    }},
    "overall_comment": "总体评价（200-300字）",
    "highlight_quotes": ["报告中的亮点句子1", "报告中的亮点句子2"],
    "improvement_areas": ["需要改进的具体方面1", "需要改进的具体方面2"]
}}

评分要求：
1. 严格按照学术标准评分，客观公正
2. 评价要具体详细，有理有据
3. 建议要具有可操作性和针对性
4. 突出报告的创新点和不足之处
5. 确保返回的是有效的JSON格式
"""

    def load_config_file(self):
        """加载配置文件"""
        try:
            if os.path.exists('deepseek_config.json'):
                with open('deepseek_config.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
        return {}

    def call_deepseek_api(self, prompt, model="deepseek-reasoner", max_tokens=4000):
        """
        调用DeepSeek API进行分析
        
        Args:
            prompt: 分析提示词
            model: 使用的模型，默认为deepseek-reasoner
            max_tokens: 最大token数
            
        Returns:
            API响应内容或None
        """
        if not self.api_key:
            print("❌ DeepSeek API密钥未设置")
            return None
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': model,
                'messages': [
                    {
                        'role': 'system', 
                        'content': '你是一位专业的计算机科学教授，擅长评估学生实验报告。请严格按照要求的JSON格式返回分析结果。'
                    },
                    {
                        'role': 'user', 
                        'content': prompt
                    }
                ],
                'temperature': 0.3,  # 较低的温度确保结果稳定
                'max_tokens': max_tokens,
                'stream': False
            }
            
            print(f"  🚀 调用DeepSeek API (模型: {model})...")
            
            response = requests.post(
                f'{self.base_url}/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60  # 60秒超时
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 显示token使用情况
                if 'usage' in result:
                    usage = result['usage']
                    print(f"  📊 Token使用: 输入{usage.get('prompt_tokens', 0)}, "
                          f"输出{usage.get('completion_tokens', 0)}, "
                          f"总计{usage.get('total_tokens', 0)}")
                
                return content
            else:
                print(f"  ❌ DeepSeek API错误: {response.status_code}")
                print(f"  错误信息: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ❌ DeepSeek API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            print(f"  ❌ DeepSeek API网络错误: {e}")
            return None
        except Exception as e:
            print(f"  ❌ DeepSeek API调用异常: {e}")
            return None

    def parse_deepseek_response(self, response_text):
        """
        解析DeepSeek API响应
        
        Args:
            response_text: API返回的文本
            
        Returns:
            解析后的分析结果字典或None
        """
        try:
            # 尝试直接解析JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # 查找JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                return json.loads(json_str)
            
            print("  ❌ 响应中未找到有效的JSON格式")
            return None
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            print(f"  响应内容: {response_text[:500]}...")
            return None

    def validate_analysis_result(self, result):
        """验证分析结果的完整性"""
        if not isinstance(result, dict):
            return False
        
        required_keys = ['scores', 'total_score', 'grade_level', 'detailed_analysis', 
                        'dimension_feedback', 'overall_comment']
        
        if not all(key in result for key in required_keys):
            print(f"  ❌ 缺少必要字段: {[k for k in required_keys if k not in result]}")
            return False
        
        # 验证scores字段
        required_dimensions = ['originality', 'completeness', 'standardization', 
                             'data_analysis', 'insight_depth']
        
        if not all(dim in result['scores'] for dim in required_dimensions):
            print(f"  ❌ scores字段不完整")
            return False
        
        # 验证分数范围
        for dim, score in result['scores'].items():
            if not isinstance(score, (int, float)) or not (0 <= score <= 20):
                print(f"  ❌ {dim}分数无效: {score}")
                return False
        
        total_score = result.get('total_score', 0)
        if not isinstance(total_score, (int, float)) or not (0 <= total_score <= 100):
            print(f"  ❌ 总分无效: {total_score}")
            return False
        
        return True

    def extract_document_content(self, file_path):
        """提取文档内容"""
        try:
            result = subprocess.run([
                'python', 'ultimate_document_reader.py', file_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=60)
            
            if result.returncode == 0:
                try:
                    output_data = json.loads(result.stdout)
                    if output_data.get('success'):
                        return output_data.get('content')
                    else:
                        print(f"  ❌ 文档提取失败: {output_data.get('status', '未知错误')}")
                        return None
                except json.JSONDecodeError:
                    print(f"  ❌ 文档提取器返回格式错误")
                    return None
            else:
                print(f"  ❌ 文档提取失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"  ❌ 文档提取异常: {e}")
            return None

    def analyze_report_with_deepseek(self, student_name, file_path):
        """使用DeepSeek分析单个报告"""
        print(f"\n{'='*70}")
        print(f"🤖 DeepSeek R1 深度分析: {student_name}")
        print(f"{'='*70}")
        
        # 步骤1: 提取文档内容
        print(f"📄 步骤1: 提取报告内容...")
        content = self.extract_document_content(file_path)
        if not content:
            print(f"❌ 无法提取报告内容")
            return None
        
        print(f"✅ 成功提取报告内容 ({len(content)} 字符, {len(content.split())} 词)")
        
        # 步骤2: 准备分析提示词
        print(f"🔍 步骤2: 准备AI分析...")
        # 限制内容长度以避免token超限
        content_for_analysis = content[:8000] if len(content) > 8000 else content
        prompt = self.analysis_prompt_template.format(report_content=content_for_analysis)
        
        # 步骤3: 调用DeepSeek API
        print(f"🚀 步骤3: DeepSeek R1 分析中...")
        api_response = self.call_deepseek_api(prompt)
        
        if not api_response:
            print(f"❌ DeepSeek API调用失败")
            return None
        
        # 步骤4: 解析响应
        print(f"📊 步骤4: 解析分析结果...")
        analysis_result = self.parse_deepseek_response(api_response)
        
        if not analysis_result:
            print(f"❌ 无法解析DeepSeek响应")
            return None
        
        # 步骤5: 验证结果
        if not self.validate_analysis_result(analysis_result):
            print(f"❌ 分析结果格式不正确")
            return None
        
        # 添加元数据
        analysis_result.update({
            'student_name': student_name,
            'file_path': file_path,
            'analysis_time': datetime.now().isoformat(),
            'content_length': len(content),
            'word_count': len(content.split()),
            'ai_model': 'deepseek-reasoner',
            'api_provider': 'DeepSeek'
        })
        
        print(f"✅ DeepSeek分析完成!")
        return analysis_result

    def display_analysis_result(self, result):
        """显示分析结果"""
        if not result:
            return
        
        print(f"\n{'='*70}")
        print(f"📊 {result['student_name']} 的DeepSeek分析报告")
        print(f"{'='*70}")
        
        print(f"🎯 总分: {result['total_score']}/100 ({result['grade_level']})")
        print(f"📄 报告长度: {result['word_count']} 词")
        print(f"🤖 AI模型: {result['ai_model']}")
        
        print(f"\n📈 各维度得分:")
        dimension_names = {
            'originality': '原创性',
            'completeness': '完整性', 
            'standardization': '规范性',
            'data_analysis': '数据分析',
            'insight_depth': '洞察深度'
        }
        
        for dim, score in result['scores'].items():
            name = dimension_names.get(dim, dim)
            bar = '█' * (score // 2) + '░' * (10 - score // 2)
            print(f"  {name:8}: {score:2d}/20 [{bar}]")
        
        print(f"\n💬 总体评价:")
        print(f"  {result['overall_comment']}")
        
        print(f"\n✨ 主要优点:")
        for i, strength in enumerate(result['detailed_analysis']['strengths'], 1):
            print(f"  {i}. {strength}")
        
        print(f"\n⚠️  需要改进:")
        for i, weakness in enumerate(result['detailed_analysis']['weaknesses'], 1):
            print(f"  {i}. {weakness}")
        
        print(f"\n💡 改进建议:")
        for i, suggestion in enumerate(result['detailed_analysis']['suggestions'], 1):
            print(f"  {i}. {suggestion}")
        
        if result.get('highlight_quotes'):
            print(f"\n🌟 报告亮点:")
            for i, quote in enumerate(result['highlight_quotes'], 1):
                print(f"  {i}. \"{quote}\"")

def main():
    if len(sys.argv) < 3:
        print("用法: python deepseek_ai_analyzer.py <学生姓名> <文件路径> [API密钥]")
        print("示例: python deepseek_ai_analyzer.py '万宇峰' '实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf'")
        print("\n环境变量设置:")
        print("  set DEEPSEEK_API_KEY=your_api_key_here")
        print("  set DEEPSEEK_BASE_URL=https://api.deepseek.com  (可选)")
        sys.exit(1)
    
    student_name = sys.argv[1]
    file_path = sys.argv[2]
    api_key = sys.argv[3] if len(sys.argv) > 3 else None
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    # 创建分析器
    analyzer = DeepSeekAIAnalyzer(api_key=api_key)
    
    # 分析报告
    result = analyzer.analyze_report_with_deepseek(student_name, file_path)
    
    if result:
        # 显示结果
        analyzer.display_analysis_result(result)
        
        # 保存详细分析
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"deepseek_analysis_{student_name}_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细分析已保存: {output_file}")
        
    else:
        print(f"❌ 分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
