#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenAI演示设置脚本
创建示例配置并演示使用方法
"""

import json
import os

def create_demo_config():
    """创建演示配置"""
    print("🔧 创建OpenAI演示配置...")
    
    # 创建示例配置（请替换为您的真实API密钥）
    demo_config = {
        "openai_api_key": "your_openai_api_key_here",  # 请替换为您的真实API密钥
        "openai_base_url": "https://api.openai.com",
        "openai_model": "gpt-4o",
        "max_tokens": 4000,
        "temperature": 0.3,
        "timeout": 120
    }
    
    # 保存配置文件
    with open('openai_config.json', 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 演示配置已创建: openai_config.json")
    print("⚠️  请编辑配置文件，填入您的真实OpenAI API密钥")
    
    return demo_config

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📖 OpenAI使用示例")
    print(f"="*60)
    
    print(f"1. 配置API密钥:")
    print(f"   编辑 openai_config.json 文件")
    print(f"   将 'your_openai_api_key_here' 替换为您的真实API密钥")
    
    print(f"\n2. 测试单个报告分析:")
    print(f"   python openai_file_analyzer.py \"万宇峰\" \"实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf\"")
    
    print(f"\n3. 批量分析（测试模式）:")
    print(f"   python batch_openai_analyzer.py")
    print(f"   选择 '1' 进行测试模式（分析前3名学生）")
    
    print(f"\n4. 批量分析（完整模式）:")
    print(f"   python batch_openai_analyzer.py")
    print(f"   选择 '2' 进行完整分析（所有学生）")

def check_prerequisites():
    """检查前置条件"""
    print(f"\n🔍 检查系统前置条件...")
    
    issues = []
    
    # 检查学生名单
    if not os.path.exists('students.json'):
        issues.append("❌ 缺少学生名单文件 students.json")
        print("   请先运行: python create_student_list.py")
    else:
        print("✅ 学生名单文件存在")
    
    # 检查报告文件夹
    if not os.path.exists('实验0-1大模型质量我来测'):
        issues.append("❌ 缺少报告文件夹")
    else:
        print("✅ 报告文件夹存在")
    
    if issues:
        print(f"\n⚠️  发现问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print(f"\n✅ 所有前置条件满足")
        return True

def create_api_key_template():
    """创建API密钥模板文件"""
    template_content = """
# OpenAI API 密钥配置说明

## 获取API密钥
1. 访问 OpenAI 官网: https://platform.openai.com/
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥

## 配置方法

### 方法1: 编辑配置文件（推荐）
编辑 openai_config.json 文件:
```json
{
  "openai_api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "openai_base_url": "https://api.openai.com",
  "openai_model": "gpt-4o"
}
```

### 方法2: 设置环境变量
```bash
set OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
set OPENAI_BASE_URL=https://api.openai.com
```

## 模型选择

### 推荐模型
- **gpt-4o**: 最新多模态模型，支持文件上传（推荐）
- **gpt-4-turbo**: 高性能模型，成本适中
- **gpt-3.5-turbo**: 经济型选择

### 成本对比（每1M tokens）
- gpt-4o: 输入$5, 输出$15
- gpt-4-turbo: 输入$10, 输出$30
- gpt-3.5-turbo: 输入$0.5, 输出$1.5

## 注意事项
- 请妥善保管您的API密钥
- 不要将API密钥提交到版本控制系统
- 定期检查API使用量和费用
- 建议设置使用限额避免意外费用
"""
    
    with open('OpenAI_API密钥配置.txt', 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print("✅ API密钥配置说明已创建: OpenAI_API密钥配置.txt")

def compare_with_deepseek():
    """对比OpenAI和DeepSeek"""
    print(f"\n📊 OpenAI vs DeepSeek 对比")
    print(f"="*60)
    
    comparison = [
        ("文件上传支持", "✅ 原生支持", "❌ 不支持"),
        ("多模态处理", "✅ 强大", "❌ 有限"),
        ("分析质量", "✅ 优秀", "✅ 良好"),
        ("成本（69学生）", "💰 ~$7.25", "💰 ~$0.20"),
        ("处理速度", "⚡ 快速", "⚡ 快速"),
        ("API稳定性", "✅ 高", "✅ 高"),
        ("配置复杂度", "🔧 简单", "🔧 简单")
    ]
    
    print(f"{'特性':<12} {'OpenAI':<15} {'DeepSeek':<15}")
    print(f"{'-'*12} {'-'*15} {'-'*15}")
    
    for feature, openai_val, deepseek_val in comparison:
        print(f"{feature:<12} {openai_val:<15} {deepseek_val:<15}")
    
    print(f"\n💡 选择建议:")
    print(f"  🎯 追求最佳质量和文件支持 → 选择OpenAI")
    print(f"  💰 注重成本控制 → 选择DeepSeek")
    print(f"  🔄 两者结合使用 → 获得最佳效果")

def main():
    print("🚀 OpenAI 实验报告AI分析系统 - 演示设置")
    print("="*70)
    
    # 检查前置条件
    prerequisites_ok = check_prerequisites()
    
    # 创建演示配置
    config = create_demo_config()
    
    # 创建API密钥配置说明
    create_api_key_template()
    
    # 显示使用示例
    show_usage_examples()
    
    # 对比分析
    compare_with_deepseek()
    
    print(f"\n🎯 下一步操作:")
    print(f"1. 获取您的OpenAI API密钥")
    print(f"2. 编辑 openai_config.json，填入真实API密钥")
    print(f"3. 运行测试: python openai_file_analyzer.py \"万宇峰\" \"报告路径\"")
    
    if not prerequisites_ok:
        print(f"\n⚠️  请先解决前置条件问题，然后再使用OpenAI分析功能")
    
    print(f"\n📚 详细使用说明请参考: OpenAI_使用说明.md")
    
    print(f"\n💡 温馨提示:")
    print(f"  - OpenAI支持直接文件上传，无需本地解析")
    print(f"  - 成本较高但质量优秀，适合重要评估")
    print(f"  - 建议先用测试模式验证效果")

if __name__ == "__main__":
    main()
