#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文档内容提取工具
用于读取学生实验报告PDF文件的内容
"""

import sys
import os
from pathlib import Path

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not available, trying pdfplumber...")
    try:
        import pdfplumber
    except ImportError:
        print("Neither PyPDF2 nor pdfplumber available")
        sys.exit(1)

def extract_text_from_pdf(pdf_path):
    """
    从PDF文件中提取文本内容
    
    Args:
        pdf_path (str): PDF文件路径
        
    Returns:
        str: 提取的文本内容
    """
    text_content = ""
    
    try:
        # 首先尝试使用PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"PDF文件页数: {len(pdf_reader.pages)}")
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += page_text + "\n"
                except Exception as e:
                    print(f"提取第{page_num + 1}页时出错: {e}")
                    
    except Exception as e:
        print(f"使用PyPDF2读取PDF失败: {e}")
        
        # 如果PyPDF2失败，尝试使用pdfplumber
        try:
            import pdfplumber
            with pdfplumber.open(pdf_path) as pdf:
                print(f"使用pdfplumber重试，PDF文件页数: {len(pdf.pages)}")
                
                for page_num, page in enumerate(pdf.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            text_content += f"\n=== 第{page_num + 1}页 ===\n"
                            text_content += page_text + "\n"
                    except Exception as e:
                        print(f"提取第{page_num + 1}页时出错: {e}")
                        
        except ImportError:
            print("pdfplumber也不可用")
        except Exception as e:
            print(f"使用pdfplumber读取PDF也失败: {e}")
    
    return text_content

def main():
    if len(sys.argv) != 2:
        print("用法: python pdf_reader.py <PDF文件路径>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"文件不存在: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.lower().endswith('.pdf'):
        print(f"不是PDF文件: {pdf_path}")
        sys.exit(1)
    
    print(f"正在读取PDF文件: {pdf_path}")
    
    text_content = extract_text_from_pdf(pdf_path)
    
    if text_content.strip():
        print("\n" + "="*50)
        print("PDF内容提取成功:")
        print("="*50)
        print(text_content)
    else:
        print("未能提取到有效的文本内容")

if __name__ == "__main__":
    main()
