#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比传统评分和AI评分的差异分析
"""

import json
import statistics
from datetime import datetime

class GradingComparison:
    def __init__(self):
        self.traditional_grades = {}
        self.ai_grades = {}
        self.comparison_results = {}
    
    def load_traditional_grades(self):
        """加载传统评分结果"""
        try:
            with open('grades.json', 'r', encoding='utf-8') as f:
                self.traditional_grades = json.load(f)
            print(f"✅ 加载传统评分数据: {len(self.traditional_grades)} 名学生")
        except Exception as e:
            print(f"❌ 加载传统评分失败: {e}")
    
    def load_ai_grades(self):
        """加载AI评分结果"""
        try:
            with open('ai_demo_results.json', 'r', encoding='utf-8') as f:
                ai_data = json.load(f)
                
            # 转换AI评分格式
            for result in ai_data['results']:
                name = result['student_name']
                self.ai_grades[name] = {
                    'scores': result['scores'],
                    'total_score': result['total_score'],
                    'grade_level': result['grade_level'],
                    'detailed_analysis': result['detailed_analysis']
                }
            
            print(f"✅ 加载AI评分数据: {len(self.ai_grades)} 名学生")
        except Exception as e:
            print(f"❌ 加载AI评分失败: {e}")
    
    def compare_grades(self):
        """对比两种评分方法"""
        print(f"\n📊 评分方法对比分析")
        print(f"="*60)
        
        # 找到共同的学生
        common_students = set(self.traditional_grades.keys()) & set(self.ai_grades.keys())
        print(f"共同评估的学生: {len(common_students)} 名")
        
        if not common_students:
            print("❌ 没有共同评估的学生，无法进行对比")
            return
        
        # 对比分析
        comparisons = []
        
        print(f"\n详细对比:")
        print(f"{'姓名':<8} {'传统总分':<8} {'AI总分':<8} {'差异':<8} {'传统等级':<8} {'AI等级':<8}")
        print(f"{'-'*60}")
        
        for student in common_students:
            trad_data = self.traditional_grades[student]
            ai_data = self.ai_grades[student]
            
            trad_score = trad_data['综合得分']
            ai_score = ai_data['total_score']
            score_diff = ai_score - trad_score
            
            trad_grade = trad_data['评分等级']
            ai_grade = ai_data['grade_level']
            
            print(f"{student:<8} {trad_score:<8} {ai_score:<8} {score_diff:+8.1f} {trad_grade:<8} {ai_grade:<8}")
            
            # 维度对比
            comparison = {
                'student_name': student,
                'traditional': {
                    'total_score': trad_score,
                    'grade_level': trad_grade,
                    'originality': trad_data['原创性'],
                    'completeness': trad_data['完整性'],
                    'standardization': trad_data['规范性'],
                    'data_analysis': trad_data['数据分析'],
                    'insight_depth': trad_data['洞察深度']
                },
                'ai': {
                    'total_score': ai_score,
                    'grade_level': ai_grade,
                    'originality': ai_data['scores']['originality'],
                    'completeness': ai_data['scores']['completeness'],
                    'standardization': ai_data['scores']['standardization'],
                    'data_analysis': ai_data['scores']['data_analysis'],
                    'insight_depth': ai_data['scores']['insight_depth']
                },
                'differences': {
                    'total_score': score_diff,
                    'originality': ai_data['scores']['originality'] - trad_data['原创性'],
                    'completeness': ai_data['scores']['completeness'] - trad_data['完整性'],
                    'standardization': ai_data['scores']['standardization'] - trad_data['规范性'],
                    'data_analysis': ai_data['scores']['data_analysis'] - trad_data['数据分析'],
                    'insight_depth': ai_data['scores']['insight_depth'] - trad_data['洞察深度']
                }
            }
            
            comparisons.append(comparison)
        
        # 统计分析
        self.analyze_differences(comparisons)
        
        # 保存对比结果
        self.save_comparison_results(comparisons)
        
        return comparisons
    
    def analyze_differences(self, comparisons):
        """分析评分差异"""
        print(f"\n📈 差异统计分析")
        print(f"="*40)
        
        # 总分差异
        total_diffs = [c['differences']['total_score'] for c in comparisons]
        avg_diff = statistics.mean(total_diffs)
        std_diff = statistics.stdev(total_diffs) if len(total_diffs) > 1 else 0
        
        print(f"总分差异统计:")
        print(f"  平均差异: {avg_diff:+.1f} 分 (AI - 传统)")
        print(f"  标准差: {std_diff:.1f}")
        print(f"  最大差异: {max(total_diffs):+.1f}")
        print(f"  最小差异: {min(total_diffs):+.1f}")
        
        # 各维度差异
        dimensions = ['originality', 'completeness', 'standardization', 'data_analysis', 'insight_depth']
        dimension_names = ['原创性', '完整性', '规范性', '数据分析', '洞察深度']
        
        print(f"\n各维度平均差异:")
        for dim, name in zip(dimensions, dimension_names):
            dim_diffs = [c['differences'][dim] for c in comparisons]
            dim_avg = statistics.mean(dim_diffs)
            print(f"  {name}: {dim_avg:+.1f}")
        
        # 等级一致性
        grade_matches = sum(1 for c in comparisons 
                           if c['traditional']['grade_level'] == c['ai']['grade_level'])
        grade_consistency = grade_matches / len(comparisons) * 100
        
        print(f"\n等级一致性: {grade_consistency:.1f}% ({grade_matches}/{len(comparisons)})")
        
        # 分析评分趋势
        ai_higher = sum(1 for diff in total_diffs if diff > 0)
        traditional_higher = sum(1 for diff in total_diffs if diff < 0)
        same_score = sum(1 for diff in total_diffs if diff == 0)
        
        print(f"\n评分趋势:")
        print(f"  AI评分更高: {ai_higher} 名学生")
        print(f"  传统评分更高: {traditional_higher} 名学生")
        print(f"  评分相同: {same_score} 名学生")
    
    def save_comparison_results(self, comparisons):
        """保存对比结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        comparison_report = {
            'comparison_info': {
                'students_compared': len(comparisons),
                'comparison_time': datetime.now().isoformat(),
                'methods': ['traditional_grading', 'ai_grading']
            },
            'detailed_comparisons': comparisons,
            'summary_statistics': self.calculate_summary_stats(comparisons)
        }
        
        # 保存JSON格式
        json_file = f"grading_comparison_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 对比结果已保存: {json_file}")
        
        # 生成CSV格式
        self.generate_comparison_csv(comparisons, timestamp)
    
    def calculate_summary_stats(self, comparisons):
        """计算汇总统计"""
        total_diffs = [c['differences']['total_score'] for c in comparisons]
        
        return {
            'total_score_differences': {
                'mean': float(statistics.mean(total_diffs)),
                'std': float(statistics.stdev(total_diffs) if len(total_diffs) > 1 else 0),
                'min': float(min(total_diffs)),
                'max': float(max(total_diffs))
            },
            'grade_consistency': {
                'matches': sum(1 for c in comparisons 
                             if c['traditional']['grade_level'] == c['ai']['grade_level']),
                'total': len(comparisons),
                'percentage': sum(1 for c in comparisons 
                                if c['traditional']['grade_level'] == c['ai']['grade_level']) / len(comparisons) * 100
            }
        }
    
    def generate_comparison_csv(self, comparisons, timestamp):
        """生成CSV格式的对比报告"""
        import csv
        
        csv_file = f"grading_comparison_{timestamp}.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '姓名', '传统总分', 'AI总分', '总分差异',
                '传统原创性', 'AI原创性', '原创性差异',
                '传统完整性', 'AI完整性', '完整性差异',
                '传统规范性', 'AI规范性', '规范性差异',
                '传统数据分析', 'AI数据分析', '数据分析差异',
                '传统洞察深度', 'AI洞察深度', '洞察深度差异',
                '传统等级', 'AI等级', '等级一致性'
            ])
            
            # 写入数据
            for comp in comparisons:
                trad = comp['traditional']
                ai = comp['ai']
                diff = comp['differences']
                
                grade_match = '一致' if trad['grade_level'] == ai['grade_level'] else '不一致'
                
                writer.writerow([
                    comp['student_name'],
                    trad['total_score'], ai['total_score'], diff['total_score'],
                    trad['originality'], ai['originality'], diff['originality'],
                    trad['completeness'], ai['completeness'], diff['completeness'],
                    trad['standardization'], ai['standardization'], diff['standardization'],
                    trad['data_analysis'], ai['data_analysis'], diff['data_analysis'],
                    trad['insight_depth'], ai['insight_depth'], diff['insight_depth'],
                    trad['grade_level'], ai['grade_level'], grade_match
                ])
        
        print(f"✅ CSV对比报告已保存: {csv_file}")
    
    def generate_insights(self, comparisons):
        """生成评分方法对比洞察"""
        print(f"\n🔍 评分方法对比洞察")
        print(f"="*40)
        
        # 分析AI评分的特点
        ai_scores = [c['ai']['total_score'] for c in comparisons]
        trad_scores = [c['traditional']['total_score'] for c in comparisons]
        
        ai_avg = statistics.mean(ai_scores)
        trad_avg = statistics.mean(trad_scores)
        
        print(f"评分特点分析:")
        print(f"  传统评分平均分: {trad_avg:.1f}")
        print(f"  AI评分平均分: {ai_avg:.1f}")
        
        if ai_avg > trad_avg:
            print(f"  AI评分总体更宽松，平均高出 {ai_avg - trad_avg:.1f} 分")
        else:
            print(f"  AI评分总体更严格，平均低出 {trad_avg - ai_avg:.1f} 分")
        
        # 维度分析
        dimensions = ['originality', 'completeness', 'standardization', 'data_analysis', 'insight_depth']
        dimension_names = ['原创性', '完整性', '规范性', '数据分析', '洞察深度']
        
        print(f"\n维度差异分析:")
        for dim, name in zip(dimensions, dimension_names):
            ai_dim_scores = [c['ai'][dim] for c in comparisons]
            trad_dim_scores = [c['traditional'][dim] for c in comparisons]
            
            ai_dim_avg = statistics.mean(ai_dim_scores)
            trad_dim_avg = statistics.mean(trad_dim_scores)
            diff = ai_dim_avg - trad_dim_avg
            
            if abs(diff) > 1:
                trend = "AI更宽松" if diff > 0 else "AI更严格"
                print(f"  {name}: {trend} (差异 {diff:+.1f})")
        
        # 一致性分析
        consistent_students = [c for c in comparisons 
                             if c['traditional']['grade_level'] == c['ai']['grade_level']]
        
        if consistent_students:
            print(f"\n评分一致的学生特点:")
            consistent_scores = [c['traditional']['total_score'] for c in consistent_students]
            print(f"  平均分: {statistics.mean(consistent_scores):.1f}")
            print(f"  分数范围: {min(consistent_scores)} - {max(consistent_scores)}")

def main():
    print("🔍 评分方法对比分析系统")
    print("="*50)
    
    comparator = GradingComparison()
    
    # 加载数据
    comparator.load_traditional_grades()
    comparator.load_ai_grades()
    
    # 执行对比
    comparisons = comparator.compare_grades()
    
    if comparisons:
        # 生成洞察
        comparator.generate_insights(comparisons)
        
        print(f"\n🎉 评分对比分析完成！")
        print(f"主要发现:")
        print(f"  - 共对比了 {len(comparisons)} 名学生的评分")
        print(f"  - AI评分提供了更详细的分析和建议")
        print(f"  - 两种方法在某些维度上存在系统性差异")
        print(f"  - 建议结合两种方法的优势进行综合评估")
    else:
        print(f"❌ 无法进行对比分析")

if __name__ == "__main__":
    main()
