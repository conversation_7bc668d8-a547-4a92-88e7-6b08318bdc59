#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级健壮的文档读取器
使用多种方法确保文档读取成功
"""

import sys
import os
import subprocess
import tempfile
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def clean_text(text):
    """清理文本，移除特殊字符和编码问题"""
    if not text:
        return ""
    
    # 移除不可打印字符，保留基本的空白字符
    cleaned = ''.join(char for char in text if char.isprintable() or char in '\n\r\t ')
    
    # 移除过多的空行
    lines = cleaned.split('\n')
    cleaned_lines = []
    empty_count = 0
    
    for line in lines:
        if line.strip():
            cleaned_lines.append(line)
            empty_count = 0
        else:
            empty_count += 1
            if empty_count <= 2:  # 最多保留2个连续空行
                cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def extract_pdf_with_pymupdf(pdf_path):
    """使用PyMuPDF (fitz)提取PDF"""
    try:
        import fitz  # PyMuPDF
        
        doc = fitz.open(pdf_path)
        text_content = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            if page_text and page_text.strip():
                text_content += f"\n=== 第{page_num + 1}页 ===\n"
                text_content += clean_text(page_text) + "\n"
        
        doc.close()
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception as e:
        print(f"PyMuPDF提取失败: {e}")
        return None

def extract_pdf_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF"""
    try:
        import PyPDF2
        
        text_content = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text(page_text) + "\n"
                except Exception as e:
                    print(f"PyPDF2提取第{page_num + 1}页失败: {e}")
                    continue
        
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception as e:
        print(f"PyPDF2提取失败: {e}")
        return None

def extract_pdf_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF"""
    try:
        import pdfplumber
        
        text_content = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text(page_text) + "\n"
                except Exception as e:
                    print(f"pdfplumber提取第{page_num + 1}页失败: {e}")
                    continue
        
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return None

def extract_pdf_with_pdfminer(pdf_path):
    """使用pdfminer提取PDF"""
    try:
        from pdfminer.high_level import extract_text
        
        text_content = extract_text(pdf_path)
        return clean_text(text_content) if text_content and text_content.strip() else None
        
    except ImportError:
        return None
    except Exception as e:
        print(f"pdfminer提取失败: {e}")
        return None

def extract_docx_with_python_docx(docx_path):
    """使用python-docx提取DOCX"""
    try:
        from docx import Document
        
        doc = Document(docx_path)
        text_content = ""
        
        # 提取段落
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content += paragraph.text + "\n"
        
        # 提取表格
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    text_content += " | ".join(row_text) + "\n"
        
        return clean_text(text_content) if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception as e:
        print(f"python-docx提取失败: {e}")
        return None

def extract_docx_with_zipfile(docx_path):
    """使用zipfile直接解析DOCX的XML"""
    try:
        text_content = ""
        
        with zipfile.ZipFile(docx_path, 'r') as docx:
            # 读取主文档
            try:
                content = docx.read('word/document.xml')
                root = ET.fromstring(content)
                
                # 提取所有文本节点
                text_parts = []
                for elem in root.iter():
                    if elem.text and elem.text.strip():
                        text_parts.append(elem.text.strip())
                
                if text_parts:
                    text_content = ' '.join(text_parts)
                    return clean_text(text_content)
                    
            except Exception as e:
                print(f"XML解析失败: {e}")
                return None
                
    except Exception as e:
        print(f"zipfile方法失败: {e}")
        return None

def extract_with_system_commands(file_path):
    """使用系统命令提取文档"""
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.pdf':
        # 尝试使用pdftotext命令
        try:
            result = subprocess.run(['pdftotext', file_path, '-'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                return clean_text(result.stdout)
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
            pass
    
    elif file_ext in ['.docx', '.doc']:
        # 尝试使用pandoc转换
        try:
            result = subprocess.run(['pandoc', file_path, '-t', 'plain'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                return clean_text(result.stdout)
        except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
            pass
    
    return None

def extract_document_super_robust(file_path):
    """超级健壮的文档内容提取"""
    if not os.path.exists(file_path):
        return None, f"文件不存在: {file_path}"
    
    file_ext = os.path.splitext(file_path)[1].lower()
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        return None, "文件为空"
    
    print(f"正在提取文档: {os.path.basename(file_path)} ({file_size:,} 字节)")
    
    content = None
    methods_tried = []
    
    if file_ext == '.pdf':
        # PDF提取方法，按优先级排序
        pdf_methods = [
            ("PyMuPDF", extract_pdf_with_pymupdf),
            ("pdfplumber", extract_pdf_with_pdfplumber),
            ("PyPDF2", extract_pdf_with_pypdf2),
            ("pdfminer", extract_pdf_with_pdfminer),
            ("系统命令", extract_with_system_commands)
        ]
        
        for method_name, method_func in pdf_methods:
            try:
                print(f"  尝试方法: {method_name}")
                if method_name == "系统命令":
                    content = method_func(file_path)
                else:
                    content = method_func(file_path)
                
                methods_tried.append(method_name)
                
                if content and len(content.strip()) > 50:
                    print(f"  [OK] {method_name} 成功，内容长度: {len(content)}")
                    return content, f"成功使用{method_name}提取"
                else:
                    print(f"  [FAIL] {method_name} 返回内容不足")
                    
            except Exception as e:
                print(f"  [ERROR] {method_name} 异常: {e}")
                methods_tried.append(f"{method_name}(失败)")
    
    elif file_ext in ['.docx', '.doc']:
        # DOCX提取方法
        docx_methods = [
            ("python-docx", extract_docx_with_python_docx),
            ("zipfile解析", extract_docx_with_zipfile),
            ("系统命令", extract_with_system_commands)
        ]
        
        for method_name, method_func in docx_methods:
            try:
                print(f"  尝试方法: {method_name}")
                if method_name == "系统命令":
                    content = method_func(file_path)
                else:
                    content = method_func(file_path)
                
                methods_tried.append(method_name)
                
                if content and len(content.strip()) > 50:
                    print(f"  [OK] {method_name} 成功，内容长度: {len(content)}")
                    return content, f"成功使用{method_name}提取"
                else:
                    print(f"  [FAIL] {method_name} 返回内容不足")
                    
            except Exception as e:
                print(f"  [ERROR] {method_name} 异常: {e}")
                methods_tried.append(f"{method_name}(失败)")
    
    else:
        return None, f"不支持的文件格式: {file_ext}"
    
    return None, f"所有方法都失败了，尝试过的方法: {', '.join(methods_tried)}"

def main():
    if len(sys.argv) != 2:
        print("用法: python super_robust_reader.py <文档文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    content, status = extract_document_super_robust(file_path)
    
    if content:
        print(f"\n状态: {status}")
        print("="*50)
        print("文档内容:")
        print("="*50)
        print(content)
    else:
        print(f"读取失败: {status}")
        sys.exit(1)

if __name__ == "__main__":
    main()
