#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文档内容提取工具
用于读取学生实验报告DOCX文件的内容
"""

import sys
import os
from pathlib import Path

def extract_text_from_docx(docx_path):
    """
    从DOCX文件中提取文本内容
    
    Args:
        docx_path (str): DOCX文件路径
        
    Returns:
        str: 提取的文本内容
    """
    text_content = ""
    
    try:
        # 尝试使用python-docx库
        try:
            from docx import Document
            
            doc = Document(docx_path)
            print(f"DOCX文件段落数: {len(doc.paragraphs)}")
            
            for i, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    text_content += paragraph.text + "\n"
                    
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content += " | ".join(row_text) + "\n"
                        
        except ImportError:
            print("python-docx库未安装，尝试使用zipfile方法...")
            
            # 备用方法：使用zipfile直接解析
            import zipfile
            import xml.etree.ElementTree as ET
            
            with zipfile.ZipFile(docx_path, 'r') as docx:
                # 读取主文档
                try:
                    content = docx.read('word/document.xml')
                    root = ET.fromstring(content)
                    
                    # 提取文本
                    for elem in root.iter():
                        if elem.text:
                            text_content += elem.text + " "
                            
                except Exception as e:
                    print(f"使用zipfile方法提取失败: {e}")
                    
    except Exception as e:
        print(f"提取DOCX内容失败: {e}")
    
    return text_content

def main():
    if len(sys.argv) != 2:
        print("用法: python docx_reader.py <DOCX文件路径>")
        sys.exit(1)
    
    docx_path = sys.argv[1]
    
    if not os.path.exists(docx_path):
        print(f"文件不存在: {docx_path}")
        sys.exit(1)
    
    if not docx_path.lower().endswith(('.docx', '.doc')):
        print(f"不是DOCX/DOC文件: {docx_path}")
        sys.exit(1)
    
    print(f"正在读取DOCX文件: {docx_path}")
    
    text_content = extract_text_from_docx(docx_path)
    
    if text_content.strip():
        print("\n" + "="*50)
        print("DOCX内容提取成功:")
        print("="*50)
        # 清理特殊字符，避免编码问题
        cleaned_content = ''.join(char for char in text_content if ord(char) < 65536 and (char.isprintable() or char in '\n\r\t '))
        print(cleaned_content)
    else:
        print("未能提取到有效的文本内容")

if __name__ == "__main__":
    main()
