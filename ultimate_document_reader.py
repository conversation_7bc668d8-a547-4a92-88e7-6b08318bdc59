#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极文档读取器
彻底解决Unicode编码问题，直接返回内容而不通过控制台输出
"""

import sys
import os
import tempfile
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path
import json

def clean_text_ultimate(text):
    """终极文本清理，移除所有可能导致编码问题的字符"""
    if not text:
        return ""
    
    # 定义允许的字符范围
    allowed_chars = []
    for char in text:
        code = ord(char)
        # 保留基本ASCII字符、中文字符和常用标点
        if (32 <= code <= 126) or \
           (0x4e00 <= code <= 0x9fff) or \
           (0x3000 <= code <= 0x303f) or \
           (0xff00 <= code <= 0xffef) or \
           char in '\n\r\t':
            allowed_chars.append(char)
        else:
            # 将特殊字符替换为相似的ASCII字符
            if char in '•·‧∙':
                allowed_chars.append('*')
            elif char in '—–−':
                allowed_chars.append('-')
            elif char in '""''':
                allowed_chars.append('"')
            elif char in '…':
                allowed_chars.append('...')
            else:
                allowed_chars.append(' ')  # 其他字符替换为空格
    
    cleaned = ''.join(allowed_chars)
    
    # 移除过多的空行和空格
    lines = cleaned.split('\n')
    cleaned_lines = []
    empty_count = 0
    
    for line in lines:
        line = line.strip()
        if line:
            cleaned_lines.append(line)
            empty_count = 0
        else:
            empty_count += 1
            if empty_count <= 2:
                cleaned_lines.append('')
    
    return '\n'.join(cleaned_lines)

def extract_pdf_with_pymupdf(pdf_path):
    """使用PyMuPDF提取PDF"""
    try:
        import fitz
        doc = fitz.open(pdf_path)
        text_content = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            if page_text and page_text.strip():
                text_content += f"\n=== 第{page_num + 1}页 ===\n"
                text_content += clean_text_ultimate(page_text) + "\n"
        
        doc.close()
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception:
        return None

def extract_pdf_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF"""
    try:
        import pdfplumber
        text_content = ""
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text_ultimate(page_text) + "\n"
                except Exception:
                    continue
        
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception:
        return None

def extract_pdf_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF"""
    try:
        import PyPDF2
        text_content = ""
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text_ultimate(page_text) + "\n"
                except Exception:
                    continue
        
        return text_content if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception:
        return None

def extract_docx_with_python_docx(docx_path):
    """使用python-docx提取DOCX"""
    try:
        from docx import Document
        doc = Document(docx_path)
        text_content = ""
        
        # 提取段落
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content += paragraph.text + "\n"
        
        # 提取表格
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    text_content += " | ".join(row_text) + "\n"
        
        return clean_text_ultimate(text_content) if text_content.strip() else None
        
    except ImportError:
        return None
    except Exception:
        return None

def extract_docx_with_zipfile(docx_path):
    """使用zipfile直接解析DOCX的XML"""
    try:
        text_content = ""
        
        with zipfile.ZipFile(docx_path, 'r') as docx:
            try:
                content = docx.read('word/document.xml')
                root = ET.fromstring(content)
                
                # 提取所有文本节点
                text_parts = []
                for elem in root.iter():
                    if elem.text and elem.text.strip():
                        text_parts.append(elem.text.strip())
                
                if text_parts:
                    text_content = ' '.join(text_parts)
                    return clean_text_ultimate(text_content)
                    
            except Exception:
                return None
                
    except Exception:
        return None

def extract_document_ultimate(file_path):
    """终极文档内容提取，直接返回结果而不通过控制台"""
    if not os.path.exists(file_path):
        return None, f"文件不存在: {file_path}"
    
    file_ext = os.path.splitext(file_path)[1].lower()
    file_size = os.path.getsize(file_path)
    
    if file_size == 0:
        return None, "文件为空"
    
    content = None
    methods_tried = []
    
    if file_ext == '.pdf':
        # PDF提取方法，按优先级排序
        pdf_methods = [
            ("PyMuPDF", extract_pdf_with_pymupdf),
            ("pdfplumber", extract_pdf_with_pdfplumber),
            ("PyPDF2", extract_pdf_with_pypdf2),
        ]
        
        for method_name, method_func in pdf_methods:
            try:
                content = method_func(file_path)
                methods_tried.append(method_name)
                
                if content and len(content.strip()) > 50:
                    return content, f"成功使用{method_name}提取"
                    
            except Exception:
                methods_tried.append(f"{method_name}(失败)")
    
    elif file_ext in ['.docx', '.doc']:
        # DOCX提取方法
        docx_methods = [
            ("python-docx", extract_docx_with_python_docx),
            ("zipfile解析", extract_docx_with_zipfile),
        ]
        
        for method_name, method_func in docx_methods:
            try:
                content = method_func(file_path)
                methods_tried.append(method_name)
                
                if content and len(content.strip()) > 50:
                    return content, f"成功使用{method_name}提取"
                    
            except Exception:
                methods_tried.append(f"{method_name}(失败)")
    
    else:
        return None, f"不支持的文件格式: {file_ext}"
    
    return None, f"所有方法都失败了，尝试过的方法: {', '.join(methods_tried)}"

def main():
    if len(sys.argv) != 2:
        result = {
            "success": False,
            "error": "用法: python ultimate_document_reader.py <文档文件路径>",
            "content": None
        }
        print(json.dumps(result, ensure_ascii=False))
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    content, status = extract_document_ultimate(file_path)
    
    result = {
        "success": content is not None,
        "status": status,
        "content": content,
        "file_path": file_path,
        "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
    }
    
    # 使用JSON格式输出，避免编码问题
    print(json.dumps(result, ensure_ascii=False))

if __name__ == "__main__":
    main()
