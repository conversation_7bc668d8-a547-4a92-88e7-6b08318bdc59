#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示AI分析器 - 分析几个代表性学生
"""

import json
import os
from batch_ai_analyzer import BatchAIAnalyzer

def demo_ai_analysis():
    """演示AI分析功能"""
    print("🤖 AI报告分析演示")
    print("="*60)
    
    # 选择几个代表性学生进行演示
    demo_students = [
        {"name": "万宇峰", "class": "软件2301"},
        {"name": "陈凌琦", "class": "软件2301"}, 
        {"name": "龚炫宇", "class": "软件2301"},
        {"name": "童聪捷", "class": "软件2302"},
        {"name": "叶子奕", "class": "软件2302"}
    ]
    
    analyzer = BatchAIAnalyzer()
    results = []
    
    for i, student in enumerate(demo_students, 1):
        student_name = student['name']
        print(f"\n[{i}/{len(demo_students)}] AI分析: {student_name}")
        
        # 查找报告文件
        report_file = analyzer.find_student_report(student_name)
        if not report_file:
            print(f"  ❌ 未找到 {student_name} 的报告文件")
            continue
        
        print(f"  📄 报告文件: {os.path.basename(report_file)}")
        
        try:
            # 执行AI分析
            analysis_result = analyzer.analyzer.analyze_report(student_name, report_file)
            
            if analysis_result:
                analysis_result['class'] = student['class']
                results.append(analysis_result)
                
                print(f"  ✅ AI分析完成")
                print(f"     总分: {analysis_result['total_score']}/100 ({analysis_result['grade_level']})")
                print(f"     各维度: 原创性{analysis_result['scores']['originality']}, "
                      f"完整性{analysis_result['scores']['completeness']}, "
                      f"规范性{analysis_result['scores']['standardization']}, "
                      f"数据分析{analysis_result['scores']['data_analysis']}, "
                      f"洞察深度{analysis_result['scores']['insight_depth']}")
            else:
                print(f"  ❌ AI分析失败")
                
        except Exception as e:
            print(f"  ❌ 分析异常: {e}")
    
    # 生成演示报告
    if results:
        print(f"\n📊 AI分析演示报告")
        print(f"="*60)
        
        # 计算统计信息
        scores = [r['total_score'] for r in results]
        avg_score = sum(scores) / len(scores)
        
        print(f"演示学生数: {len(results)}")
        print(f"平均分: {avg_score:.1f}/100")
        print(f"分数范围: {min(scores)} - {max(scores)}")
        
        # 各维度平均分
        dimensions = ['originality', 'completeness', 'standardization', 'data_analysis', 'insight_depth']
        dimension_names = ['原创性', '完整性', '规范性', '数据分析', '洞察深度']
        
        print(f"\n各维度平均分:")
        for dim, name in zip(dimensions, dimension_names):
            dim_scores = [r['scores'][dim] for r in results]
            dim_avg = sum(dim_scores) / len(dim_scores)
            print(f"  {name}: {dim_avg:.1f}/20")
        
        # 详细结果
        print(f"\n详细分析结果:")
        print(f"{'姓名':<8} {'总分':<6} {'等级':<8} {'原创性':<6} {'完整性':<6} {'规范性':<6} {'数据分析':<8} {'洞察深度':<8}")
        print(f"{'-'*70}")
        
        for result in results:
            scores = result['scores']
            print(f"{result['student_name']:<8} "
                  f"{result['total_score']:<6} "
                  f"{result['grade_level']:<8} "
                  f"{scores['originality']:<6} "
                  f"{scores['completeness']:<6} "
                  f"{scores['standardization']:<6} "
                  f"{scores['data_analysis']:<8} "
                  f"{scores['insight_depth']:<8}")
        
        # 保存演示结果
        demo_report = {
            'demo_info': {
                'students_analyzed': len(results),
                'average_score': avg_score,
                'score_range': [min(scores), max(scores)]
            },
            'results': results
        }
        
        with open('ai_demo_results.json', 'w', encoding='utf-8') as f:
            json.dump(demo_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 演示结果已保存: ai_demo_results.json")
        
        # 显示一个详细的分析示例
        if results:
            sample = results[0]
            print(f"\n📋 详细分析示例 - {sample['student_name']}")
            print(f"="*50)
            print(f"总体评价: {sample['overall_comment']}")
            print(f"\n主要优点:")
            for strength in sample['detailed_analysis']['strengths']:
                print(f"  + {strength}")
            print(f"\n需要改进:")
            for weakness in sample['detailed_analysis']['weaknesses']:
                print(f"  - {weakness}")
            print(f"\n改进建议:")
            for suggestion in sample['detailed_analysis']['suggestions']:
                print(f"  → {suggestion}")
    
    else:
        print(f"\n❌ 没有成功分析的学生")

if __name__ == "__main__":
    demo_ai_analysis()
