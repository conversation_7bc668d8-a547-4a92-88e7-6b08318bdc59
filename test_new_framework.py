#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的评分框架
验证DeepSeek系统是否正确使用实验0-1评分框架
"""

import json
from deepseek_ai_analyzer import DeepSeekAIAnalyzer

def display_framework():
    """显示当前使用的评分框架"""
    print("🎯 当前DeepSeek系统使用的评分框架")
    print("="*80)
    
    analyzer = DeepSeekAIAnalyzer()
    
    # 提取评分框架部分
    prompt_template = analyzer.analysis_prompt_template
    
    # 找到评分框架部分
    framework_start = prompt_template.find("## 评分框架")
    framework_end = prompt_template.find("## 评分等级转换")
    
    if framework_start != -1 and framework_end != -1:
        framework_content = prompt_template[framework_start:framework_end].strip()
        print(framework_content)
    
    # 显示等级转换
    grade_start = prompt_template.find("## 评分等级转换")
    grade_end = prompt_template.find("请仔细阅读以下学生报告内容")
    
    if grade_start != -1 and grade_end != -1:
        grade_content = prompt_template[grade_start:grade_end].strip()
        print("\n" + grade_content)

def test_framework_compliance():
    """测试评分框架合规性"""
    print("\n🔍 评分框架合规性检查")
    print("="*50)
    
    analyzer = DeepSeekAIAnalyzer()
    prompt = analyzer.analysis_prompt_template
    
    # 检查关键要素
    checks = [
        ("原创性评分标准", "AI辅助内容<30%" in prompt),
        ("完整性必含要素", "3个模型测试记录" in prompt),
        ("规范性扣分规则", "错别字/病句" in prompt),
        ("数据分析等级", "含对比图表+数据交叉分析" in prompt),
        ("洞察深度标准", "指出模型特定场景缺陷" in prompt),
        ("评分证据要求", "scoring_evidence" in prompt),
        ("等级转换规则", "90-100分：优秀" in prompt)
    ]
    
    all_passed = True
    for check_name, result in checks:
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查通过！评分框架已正确更新")
    else:
        print("\n⚠️  部分检查未通过，需要进一步调整")
    
    return all_passed

def compare_frameworks():
    """对比新旧评分框架的差异"""
    print("\n📊 新旧评分框架对比")
    print("="*60)
    
    print("🔄 主要变化：")
    changes = [
        "✅ 原创性：从'创新思维'改为'AI辅助内容比例'评估",
        "✅ 完整性：明确了5个必含要素和具体扣分规则",
        "✅ 规范性：详细的扣分规则（错别字、逻辑矛盾等）",
        "✅ 数据分析：从'科学性'改为'图表对比深度'",
        "✅ 洞察深度：强调'可操作优化方案'",
        "✅ 增加了评分证据要求，提高可追溯性",
        "✅ 等级划分更加明确和具体"
    ]
    
    for change in changes:
        print(f"  {change}")

def test_with_sample():
    """使用样本测试新框架"""
    print("\n🧪 样本测试")
    print("="*40)
    
    # 检查配置
    analyzer = DeepSeekAIAnalyzer()
    if not analyzer.api_key:
        print("⚠️  API密钥未配置，跳过实际测试")
        print("💡 要进行实际测试，请运行：")
        print("   python deepseek_ai_analyzer.py \"万宇峰\" \"报告路径\"")
        return
    
    print("✅ API配置正常，可以进行实际测试")
    print("💡 建议运行以下命令测试新框架：")
    print("   python deepseek_ai_analyzer.py \"万宇峰\" \"实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf\"")

def main():
    print("🎯 实验0-1评分框架验证系统")
    print("="*80)
    
    # 显示当前框架
    display_framework()
    
    # 检查合规性
    compliance_ok = test_framework_compliance()
    
    # 对比变化
    compare_frameworks()
    
    # 测试建议
    test_with_sample()
    
    print(f"\n📋 总结")
    print(f"="*30)
    if compliance_ok:
        print("✅ DeepSeek系统已成功更新为实验0-1评分框架")
        print("✅ 所有评分标准都已正确配置")
        print("✅ 可以开始使用新框架进行评分")
        
        print(f"\n🚀 下一步操作：")
        print("1. 测试单个报告：python deepseek_ai_analyzer.py \"学生姓名\" \"报告路径\"")
        print("2. 批量分析：python batch_deepseek_analyzer.py")
        print("3. 对比新旧评分结果的差异")
    else:
        print("❌ 评分框架更新不完整，需要进一步调整")

if __name__ == "__main__":
    main()
