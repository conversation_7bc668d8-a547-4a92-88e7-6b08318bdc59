# 实验0-1-实验报告评分框架

## 评分维度与权重
| 维度 | 权重 | 说明 |
|------|------|------|
| **原创性** | 20% | 自主完成程度与AI生成内容比例 |
| **完整性** | 20% | 实验要求内容的完整覆盖度 |
| **规范性** | 20% | 报告表述的清晰度与规范程度 |
| **数据分析** | 20% | 数据处理与模型比较深度 |
| **洞察深度** | 20% | 问题发现与改进建议价值 |

---

## 评分标准细则

### 原创性（20分）
| 等级 | 分值 | 标准 |
|------|------|------|
| 优秀 | 18-20 | 人工分析痕迹明显，AI辅助内容<30% |
| 良好 | 15-17 | 关键分析自主完成，AI辅助30%-50% |
| 中等 | 12-14 | 框架自主搭建，核心内容依赖AI |
| 及格 | 9-11 | 直接使用AI生成内容>70% |
| 不及格 | 0-8 | 全文AI生成无修改痕迹 |

### 完整性（20分）
**必含要素清单**：
- 3个模型测试记录（-4分/缺失）
- 12个提示词执行结果（-1分/缺失）
- 4维度评分表（-5分/缺失）
- 综合质量分值计算（-3分/缺失）
- 结论与建议（-5分/缺失）

### 规范性（20分）
**扣分规则**：
- 错别字/病句（-0.5分/处，上限-5分）
- 逻辑矛盾（-2分/处）
- 图表无标题/单位（-3分/项）
- 报告结构缺失（-5分/部分）

### 数据分析（20分）
| 等级 | 标准 |
|------|------|
| 优秀 | 含对比图表+数据交叉分析 |
| 良好 | 有数据汇总但缺乏对比 |
| 中等 | 仅罗列原始数据 |
| 及格 | 数据记录不全 |
| 不及格 | 无有效数据 |

### 洞察深度（20分）
| 等级 | 标准 |
|------|------|
| 优秀 | 指出模型特定场景缺陷+可操作优化方案 |
| 良好 | 准确归纳各模型优缺点 |
| 中等 | 仅描述表面现象 |
| 及格 | 结论与数据脱节 |
| 不及格 | 无有效结论 |

---

## 评分等级转换规则
| 总分区间 | 等级 |
|----------|------|
| 90-100 | 优秀 |
| 80-89 | 良好 |
| 70-79 | 中等 |
| 60-69 | 及格 |
| 0-59 | 不及格 |

---

## AI评分执行模板
| 学生姓名 | 原创性 | 完整性 | 规范性 | 数据分析 | 洞察深度 | 综合得分 | 评分等级 | 评分原因（可追溯证据） |
|----------|--------|--------|--------|----------|----------|----------|----------|------------------------|
| 张三 | 18/20 | 16/20 | 20/20 | 18/20 | 17/20 | 89/100 | 良好 | 1. 原创性：模型对比含自建评估矩阵（+5）<br>2. 完整性：缺少2个提示词结果（-2）<br>3. 规范性：图表3无单位（-1）<br>4. 数据分析：含准确柱状图对比（+4）<br>5. 洞察：建议未关联数据缺陷（-3） |
| 李四 | 14/20 | 12/20 | 18/20 | 15/20 | 10/20 | 69/100 | 及格 | 1. 原创性：核心分析依赖AI生成（-6）<br>2. 完整性：缺失质量分值计算（-3）<br>3. 规范性：2处逻辑矛盾（-4）<br>4. 数据分析：仅罗列原始数据（-5）<br>5. 洞察：结论与数据脱节（-8） |

> **AI操作指引**：
> 1. 原创性：扫描「自述」/「AI辅助」标注段落比例
> 2. 完整性：检查章节标题关键词覆盖率
> 3. 规范性：调用语法检查API+人工规则库
> 4. 数据分析：提取图表/数据对比关键词
> 5. 洞察深度：识别「优化/局限/根本原因」等深度词汇
> 
> *注：实际评分需填充具体证据，如"规范性扣分：图2标题缺失坐标单位（-1分）"*