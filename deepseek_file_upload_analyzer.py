#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek文件上传分析器
直接上传PDF/DOCX文件给DeepSeek进行分析，无需本地解析
"""

import json
import os
import sys
import requests
import base64
import time
from datetime import datetime

class DeepSeekFileUploadAnalyzer:
    def __init__(self, api_key=None, base_url=None):
        """
        初始化DeepSeek文件上传分析器
        
        Args:
            api_key: DeepSeek API密钥
            base_url: DeepSeek API基础URL
        """
        # 加载配置
        config = self.load_config_file()
        
        self.api_key = api_key or config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
        self.base_url = base_url or config.get('base_url') or os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
        
        if not self.api_key:
            print("⚠️  警告：未设置DeepSeek API密钥")
            print("请设置环境变量 DEEPSEEK_API_KEY 或在配置文件中设置")
        
        # 评分框架提示词
        self.analysis_prompt_template = """
你是一位资深的计算机科学教授，拥有20年的教学经验，专门评估学生的实验报告。请严格按照以下评分框架对上传的实验报告文件进行评估。

实验背景：
- 课程：大数据与人工智能
- 实验题目：大语言模型质量我来测
- 要求：学生需要测试不同的大语言模型，比较其性能，撰写分析报告

## 评分框架（总分100分，每维度20分）

### 1. 原创性（20分）- 自主完成程度与AI生成内容比例
- 优秀(18-20分)：人工分析痕迹明显，AI辅助内容<30%
- 良好(15-17分)：关键分析自主完成，AI辅助30%-50%
- 中等(12-14分)：框架自主搭建，核心内容依赖AI
- 及格(9-11分)：直接使用AI生成内容>70%
- 不及格(0-8分)：全文AI生成无修改痕迹

### 2. 完整性（20分）- 实验要求内容的完整覆盖度
必含要素检查：
- 3个模型测试记录（缺失扣4分/个）
- 12个提示词执行结果（缺失扣1分/个）
- 4维度评分表（缺失扣5分）
- 综合质量分值计算（缺失扣3分）
- 结论与建议（缺失扣5分）

### 3. 规范性（20分）- 报告表述的清晰度与规范程度
扣分规则：
- 错别字/病句（-0.5分/处，上限-5分）
- 逻辑矛盾（-2分/处）
- 图表无标题/单位（-3分/项）
- 报告结构缺失（-5分/部分）

### 4. 数据分析（20分）- 数据处理与模型比较深度
- 优秀(18-20分)：含对比图表+数据交叉分析
- 良好(15-17分)：有数据汇总但缺乏对比
- 中等(12-14分)：仅罗列原始数据
- 及格(9-11分)：数据记录不全
- 不及格(0-8分)：无有效数据

### 5. 洞察深度（20分）- 问题发现与改进建议价值
- 优秀(18-20分)：指出模型特定场景缺陷+可操作优化方案
- 良好(15-17分)：准确归纳各模型优缺点
- 中等(12-14分)：仅描述表面现象
- 及格(9-11分)：结论与数据脱节
- 不及格(0-8分)：无有效结论

## 评分等级转换
- 90-100分：优秀
- 80-89分：良好
- 70-79分：中等
- 60-69分：及格
- 0-59分：不及格

请仔细阅读上传的报告文件，严格按照上述评分框架进行评估。

请严格按照以下JSON格式返回分析结果：

{{
    "scores": {{
        "originality": 分数(0-20的整数),
        "completeness": 分数(0-20的整数),
        "standardization": 分数(0-20的整数),
        "data_analysis": 分数(0-20的整数),
        "insight_depth": 分数(0-20的整数)
    }},
    "total_score": 总分(0-100的整数),
    "grade_level": "优秀/良好/中等/及格/不及格",
    "detailed_analysis": {{
        "strengths": ["具体优点1", "具体优点2", "具体优点3"],
        "weaknesses": ["具体不足1", "具体不足2", "具体不足3"],
        "suggestions": ["具体改进建议1", "具体改进建议2", "具体改进建议3"]
    }},
    "dimension_feedback": {{
        "originality": "原创性评价：具体分析AI辅助比例和自主分析痕迹",
        "completeness": "完整性评价：检查必含要素完成情况，明确扣分项",
        "standardization": "规范性评价：指出具体的格式、语言、逻辑问题",
        "data_analysis": "数据分析评价：评估图表质量和数据对比深度",
        "insight_depth": "洞察深度评价：分析结论质量和改进建议价值"
    }},
    "overall_comment": "总体评价（200-300字）",
    "scoring_evidence": {{
        "originality_evidence": "原创性评分依据：具体指出AI生成痕迹或自主分析证据",
        "completeness_evidence": "完整性评分依据：列出缺失的必含要素",
        "standardization_evidence": "规范性评分依据：具体的扣分项和位置",
        "data_analysis_evidence": "数据分析评分依据：图表和数据处理质量评估",
        "insight_depth_evidence": "洞察深度评分依据：结论质量和建议可操作性分析"
    }},
    "improvement_areas": ["需要改进的具体方面1", "需要改进的具体方面2"]
}}

评分要求：
1. 严格按照实验0-1评分框架执行，不得偏离标准
2. 每个维度必须提供具体的评分证据和扣分理由
3. 完整性评分必须检查所有必含要素
4. 规范性评分必须统计具体的错误数量
5. 确保返回的是有效的JSON格式
"""

    def load_config_file(self):
        """加载配置文件"""
        try:
            if os.path.exists('deepseek_config.json'):
                with open('deepseek_config.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  加载配置文件失败: {e}")
        return {}

    def encode_file_to_base64(self, file_path):
        """将文件编码为base64"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
                return base64.b64encode(file_content).decode('utf-8')
        except Exception as e:
            print(f"❌ 文件编码失败: {e}")
            return None

    def get_file_mime_type(self, file_path):
        """获取文件MIME类型"""
        file_ext = os.path.splitext(file_path)[1].lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword'
        }
        return mime_types.get(file_ext, 'application/octet-stream')

    def call_deepseek_file_api(self, file_path, model="deepseek-reasoner"):
        """
        调用DeepSeek文件上传API
        
        Args:
            file_path: 文件路径
            model: 使用的模型
            
        Returns:
            API响应内容或None
        """
        if not self.api_key:
            print("❌ DeepSeek API密钥未设置")
            return None
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        # 检查文件大小（限制为10MB）
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            print(f"❌ 文件过大: {file_size / (1024*1024):.1f}MB，超过10MB限制")
            return None
        
        try:
            # 编码文件
            print(f"  📄 编码文件: {os.path.basename(file_path)} ({file_size:,} 字节)")
            file_base64 = self.encode_file_to_base64(file_path)
            if not file_base64:
                return None
            
            # 获取文件类型
            mime_type = self.get_file_mime_type(file_path)
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # 构建请求数据 - 使用DeepSeek支持的格式
            data = {
                'model': model,
                'messages': [
                    {
                        'role': 'system',
                        'content': '你是一位专业的计算机科学教授，擅长评估学生实验报告。请严格按照要求的JSON格式返回分析结果。'
                    },
                    {
                        'role': 'user',
                        'content': f"{self.analysis_prompt_template}\n\n请分析以下上传的文档文件：\n文件名: {os.path.basename(file_path)}\n文件类型: {mime_type}\n文件大小: {file_size} 字节"
                    }
                ],
                'temperature': 0.3,
                'max_tokens': 4000,
                'stream': False,
                'files': [
                    {
                        'data': file_base64,
                        'mime_type': mime_type,
                        'filename': os.path.basename(file_path)
                    }
                ]
            }
            
            print(f"  🚀 上传文件到DeepSeek API (模型: {model})...")
            
            response = requests.post(
                f'{self.base_url}/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=120  # 文件上传需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 显示token使用情况
                if 'usage' in result:
                    usage = result['usage']
                    print(f"  📊 Token使用: 输入{usage.get('prompt_tokens', 0)}, "
                          f"输出{usage.get('completion_tokens', 0)}, "
                          f"总计{usage.get('total_tokens', 0)}")
                
                return content
            else:
                print(f"  ❌ DeepSeek API错误: {response.status_code}")
                print(f"  错误信息: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ❌ DeepSeek API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            print(f"  ❌ DeepSeek API网络错误: {e}")
            return None
        except Exception as e:
            print(f"  ❌ DeepSeek API调用异常: {e}")
            return None

    def parse_deepseek_response(self, response_text):
        """解析DeepSeek API响应"""
        try:
            # 尝试直接解析JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # 查找JSON部分
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                return json.loads(json_str)
            
            print("  ❌ 响应中未找到有效的JSON格式")
            return None
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            print(f"  响应内容: {response_text[:500]}...")
            return None

    def validate_analysis_result(self, result):
        """验证分析结果的完整性"""
        if not isinstance(result, dict):
            return False
        
        required_keys = ['scores', 'total_score', 'grade_level', 'detailed_analysis', 
                        'dimension_feedback', 'overall_comment']
        
        if not all(key in result for key in required_keys):
            print(f"  ❌ 缺少必要字段: {[k for k in required_keys if k not in result]}")
            return False
        
        # 验证scores字段
        required_dimensions = ['originality', 'completeness', 'standardization', 
                             'data_analysis', 'insight_depth']
        
        if not all(dim in result['scores'] for dim in required_dimensions):
            print(f"  ❌ scores字段不完整")
            return False
        
        # 验证分数范围
        for dim, score in result['scores'].items():
            if not isinstance(score, (int, float)) or not (0 <= score <= 20):
                print(f"  ❌ {dim}分数无效: {score}")
                return False
        
        total_score = result.get('total_score', 0)
        if not isinstance(total_score, (int, float)) or not (0 <= total_score <= 100):
            print(f"  ❌ 总分无效: {total_score}")
            return False
        
        return True

    def analyze_report_with_file_upload(self, student_name, file_path):
        """使用文件上传方式分析报告"""
        print(f"\n{'='*70}")
        print(f"📁 DeepSeek文件上传分析: {student_name}")
        print(f"{'='*70}")
        
        # 步骤1: 检查文件
        print(f"📄 步骤1: 检查报告文件...")
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return None
        
        file_size = os.path.getsize(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext not in ['.pdf', '.docx', '.doc']:
            print(f"❌ 不支持的文件格式: {file_ext}")
            return None
        
        print(f"✅ 文件检查通过: {os.path.basename(file_path)} ({file_size:,} 字节)")
        
        # 步骤2: 上传文件到DeepSeek
        print(f"🚀 步骤2: 上传文件到DeepSeek进行分析...")
        api_response = self.call_deepseek_file_api(file_path)
        
        if not api_response:
            print(f"❌ 文件上传分析失败")
            return None
        
        # 步骤3: 解析响应
        print(f"📊 步骤3: 解析分析结果...")
        analysis_result = self.parse_deepseek_response(api_response)
        
        if not analysis_result:
            print(f"❌ 无法解析DeepSeek响应")
            return None
        
        # 步骤4: 验证结果
        if not self.validate_analysis_result(analysis_result):
            print(f"❌ 分析结果格式不正确")
            return None
        
        # 添加元数据
        analysis_result.update({
            'student_name': student_name,
            'file_path': file_path,
            'analysis_time': datetime.now().isoformat(),
            'file_size': file_size,
            'file_type': file_ext,
            'ai_model': 'deepseek-reasoner',
            'api_provider': 'DeepSeek',
            'analysis_method': 'file_upload'
        })
        
        print(f"✅ 文件上传分析完成!")
        return analysis_result

    def display_analysis_result(self, result):
        """显示分析结果"""
        if not result:
            return
        
        print(f"\n{'='*70}")
        print(f"📊 {result['student_name']} 的DeepSeek文件分析报告")
        print(f"{'='*70}")
        
        print(f"🎯 总分: {result['total_score']}/100 ({result['grade_level']})")
        print(f"📄 文件: {os.path.basename(result['file_path'])}")
        print(f"📁 文件大小: {result['file_size']:,} 字节")
        print(f"🤖 分析方法: 文件直接上传")
        
        print(f"\n📈 各维度得分:")
        dimension_names = {
            'originality': '原创性',
            'completeness': '完整性', 
            'standardization': '规范性',
            'data_analysis': '数据分析',
            'insight_depth': '洞察深度'
        }
        
        for dim, score in result['scores'].items():
            name = dimension_names.get(dim, dim)
            bar = '█' * (score // 2) + '░' * (10 - score // 2)
            print(f"  {name:8}: {score:2d}/20 [{bar}]")
        
        print(f"\n💬 总体评价:")
        print(f"  {result['overall_comment']}")
        
        print(f"\n✨ 主要优点:")
        for i, strength in enumerate(result['detailed_analysis']['strengths'], 1):
            print(f"  {i}. {strength}")
        
        print(f"\n⚠️  需要改进:")
        for i, weakness in enumerate(result['detailed_analysis']['weaknesses'], 1):
            print(f"  {i}. {weakness}")
        
        print(f"\n💡 改进建议:")
        for i, suggestion in enumerate(result['detailed_analysis']['suggestions'], 1):
            print(f"  {i}. {suggestion}")

def main():
    if len(sys.argv) < 3:
        print("用法: python deepseek_file_upload_analyzer.py <学生姓名> <文件路径>")
        print("示例: python deepseek_file_upload_analyzer.py '万宇峰' '实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf'")
        sys.exit(1)
    
    student_name = sys.argv[1]
    file_path = sys.argv[2]
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    # 创建分析器
    analyzer = DeepSeekFileUploadAnalyzer()
    
    # 分析报告
    result = analyzer.analyze_report_with_file_upload(student_name, file_path)
    
    if result:
        # 显示结果
        analyzer.display_analysis_result(result)
        
        # 保存详细分析
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"deepseek_file_analysis_{student_name}_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细分析已保存: {output_file}")
        
    else:
        print(f"❌ 分析失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
