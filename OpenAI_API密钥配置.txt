
# OpenAI API 密钥配置说明

## 获取API密钥
1. 访问 OpenAI 官网: https://platform.openai.com/
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥

## 配置方法

### 方法1: 编辑配置文件（推荐）
编辑 openai_config.json 文件:
```json
{
  "openai_api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "openai_base_url": "https://api.openai.com",
  "openai_model": "gpt-4o"
}
```

### 方法2: 设置环境变量
```bash
set OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
set OPENAI_BASE_URL=https://api.openai.com
```

## 模型选择

### 推荐模型
- **gpt-4o**: 最新多模态模型，支持文件上传（推荐）
- **gpt-4-turbo**: 高性能模型，成本适中
- **gpt-3.5-turbo**: 经济型选择

### 成本对比（每1M tokens）
- gpt-4o: 输入$5, 输出$15
- gpt-4-turbo: 输入$10, 输出$30
- gpt-3.5-turbo: 输入$0.5, 输出$1.5

## 注意事项
- 请妥善保管您的API密钥
- 不要将API密钥提交到版本控制系统
- 定期检查API使用量和费用
- 建议设置使用限额避免意外费用
