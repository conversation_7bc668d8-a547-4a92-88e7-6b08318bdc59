#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI驱动的实验报告分析器
使用大语言模型进行深度报告分析
"""

import json
import os
import subprocess
import sys
from datetime import datetime

class AIReportAnalyzer:
    def __init__(self):
        self.analysis_prompt = """
你是一位资深的计算机科学教授，专门评估学生的实验报告。请对以下实验报告进行全面分析：

实验题目：大语言模型质量我来测

评估维度：
1. 原创性 (0-20分)：内容的独创性、个人见解的深度
2. 完整性 (0-20分)：实验步骤的完整性、数据的充分性
3. 规范性 (0-20分)：格式规范、引用标准、语言表达
4. 数据分析 (0-20分)：数据处理的科学性、分析方法的合理性
5. 洞察深度 (0-20分)：结论的深刻性、对问题的理解程度

请分析以下报告内容：

{report_content}

请按以下JSON格式返回分析结果：
{{
    "scores": {{
        "originality": 分数,
        "completeness": 分数,
        "standardization": 分数,
        "data_analysis": 分数,
        "insight_depth": 分数
    }},
    "total_score": 总分,
    "grade_level": "优秀/良好/中等/及格/不及格",
    "detailed_analysis": {{
        "strengths": ["优点1", "优点2", "优点3"],
        "weaknesses": ["不足1", "不足2", "不足3"],
        "suggestions": ["建议1", "建议2", "建议3"]
    }},
    "dimension_feedback": {{
        "originality": "原创性评价",
        "completeness": "完整性评价", 
        "standardization": "规范性评价",
        "data_analysis": "数据分析评价",
        "insight_depth": "洞察深度评价"
    }},
    "overall_comment": "总体评价"
}}

请确保评分客观公正，评价具体详细，建议具有可操作性。
"""

    def analyze_with_ai(self, report_content, student_name):
        """使用AI分析报告内容"""
        try:
            # 准备提示词
            prompt = self.analysis_prompt.format(report_content=report_content[:8000])  # 限制长度
            
            # 保存提示词到临时文件
            temp_prompt_file = f"temp_prompt_{student_name}.txt"
            with open(temp_prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            
            print(f"  正在使用AI分析 {student_name} 的报告...")
            
            # 这里可以调用不同的AI服务
            # 示例：使用本地AI模型或API
            analysis_result = self._call_ai_service(prompt)
            
            # 清理临时文件
            if os.path.exists(temp_prompt_file):
                os.remove(temp_prompt_file)
            
            return analysis_result
            
        except Exception as e:
            print(f"  AI分析失败: {e}")
            return self._fallback_analysis()
    
    def _call_ai_service(self, prompt):
        """调用AI服务进行分析"""
        # 这里可以集成不同的AI服务
        # 1. OpenAI GPT API
        # 2. 本地部署的模型
        # 3. 其他AI服务
        
        # 示例：模拟AI分析结果
        return self._simulate_ai_analysis(prompt)
    
    def _simulate_ai_analysis(self, prompt):
        """模拟AI分析（用于演示）"""
        # 基于报告内容长度和关键词进行简单分析
        content_length = len(prompt)
        
        # 基础分数
        base_scores = {
            "originality": 15,
            "completeness": 12,
            "standardization": 16,
            "data_analysis": 10,
            "insight_depth": 10
        }
        
        # 根据内容长度调整分数
        if content_length > 5000:
            base_scores["completeness"] += 2
            base_scores["insight_depth"] += 1
        
        # 检查关键词
        keywords = ["实验", "分析", "结论", "数据", "模型", "测试", "评估"]
        keyword_count = sum(1 for keyword in keywords if keyword in prompt)
        
        if keyword_count >= 5:
            base_scores["originality"] += 2
            base_scores["data_analysis"] += 2
        
        total_score = sum(base_scores.values())
        
        # 确定等级
        if total_score >= 90:
            grade_level = "优秀"
        elif total_score >= 80:
            grade_level = "良好"
        elif total_score >= 70:
            grade_level = "中等"
        elif total_score >= 60:
            grade_level = "及格"
        else:
            grade_level = "不及格"
        
        return {
            "scores": base_scores,
            "total_score": total_score,
            "grade_level": grade_level,
            "detailed_analysis": {
                "strengths": [
                    "实验设计较为完整",
                    "数据收集方法合理",
                    "报告结构清晰"
                ],
                "weaknesses": [
                    "数据分析深度有待提高",
                    "结论部分可以更加深入",
                    "创新性思考不足"
                ],
                "suggestions": [
                    "增加更多的数据分析方法",
                    "深化对实验结果的讨论",
                    "加入个人独特见解"
                ]
            },
            "dimension_feedback": {
                "originality": f"原创性得分{base_scores['originality']}/20，体现了一定的个人思考",
                "completeness": f"完整性得分{base_scores['completeness']}/20，实验步骤基本完整",
                "standardization": f"规范性得分{base_scores['standardization']}/20，格式较为规范",
                "data_analysis": f"数据分析得分{base_scores['data_analysis']}/20，分析方法需要改进",
                "insight_depth": f"洞察深度得分{base_scores['insight_depth']}/20，理解程度有待提升"
            },
            "overall_comment": f"总体而言，这是一份{grade_level}的实验报告。报告结构完整，但在数据分析和洞察深度方面还有提升空间。建议加强对实验结果的深入分析和个人见解的表达。"
        }
    
    def _fallback_analysis(self):
        """AI分析失败时的备用分析"""
        return {
            "scores": {
                "originality": 15,
                "completeness": 12,
                "standardization": 16,
                "data_analysis": 8,
                "insight_depth": 8
            },
            "total_score": 59,
            "grade_level": "不及格",
            "detailed_analysis": {
                "strengths": ["报告格式基本规范"],
                "weaknesses": ["AI分析失败，无法获得详细评价"],
                "suggestions": ["请检查报告内容并重新提交"]
            },
            "dimension_feedback": {
                "originality": "AI分析失败，使用默认评分",
                "completeness": "AI分析失败，使用默认评分",
                "standardization": "AI分析失败，使用默认评分",
                "data_analysis": "AI分析失败，使用默认评分",
                "insight_depth": "AI分析失败，使用默认评分"
            },
            "overall_comment": "由于AI分析失败，此评分为系统默认评分，建议人工复核。"
        }

    def extract_document_content(self, file_path):
        """提取文档内容"""
        try:
            result = subprocess.run([
                'python', 'ultimate_document_reader.py', file_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=60)
            
            if result.returncode == 0:
                import json
                try:
                    output_data = json.loads(result.stdout)
                    if output_data.get('success'):
                        return output_data.get('content')
                    else:
                        print(f"  文档提取失败: {output_data.get('status', '未知错误')}")
                        return None
                except json.JSONDecodeError:
                    print(f"  JSON解析失败")
                    return None
            else:
                print(f"  文档提取失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"  文档提取异常: {e}")
            return None

    def analyze_single_report(self, student_name, file_path):
        """分析单个学生报告"""
        print(f"\n=== 分析学生: {student_name} ===")
        
        # 提取文档内容
        content = self.extract_document_content(file_path)
        if not content:
            return None
        
        # AI分析
        analysis = self.analyze_with_ai(content, student_name)
        
        # 添加学生信息
        analysis['student_name'] = student_name
        analysis['file_path'] = file_path
        analysis['analysis_time'] = datetime.now().isoformat()
        
        return analysis

def main():
    if len(sys.argv) < 2:
        print("用法: python ai_report_analyzer.py <学生姓名> [文件路径]")
        print("或者: python ai_report_analyzer.py --batch")
        sys.exit(1)
    
    analyzer = AIReportAnalyzer()
    
    if sys.argv[1] == "--batch":
        print("=== AI驱动的批量报告分析 ===")
        # 批量分析逻辑将在后续实现
        print("批量分析功能开发中...")
    else:
        # 单个报告分析
        student_name = sys.argv[1]
        file_path = sys.argv[2] if len(sys.argv) > 2 else None
        
        if not file_path:
            print(f"请提供 {student_name} 的报告文件路径")
            sys.exit(1)
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            sys.exit(1)
        
        # 分析报告
        result = analyzer.analyze_single_report(student_name, file_path)
        
        if result:
            # 输出分析结果
            print(f"\n=== {student_name} 的AI分析结果 ===")
            print(f"总分: {result['total_score']}/100")
            print(f"等级: {result['grade_level']}")
            print(f"\n各维度得分:")
            for dim, score in result['scores'].items():
                print(f"  {dim}: {score}/20")
            
            print(f"\n总体评价:")
            print(f"  {result['overall_comment']}")
            
            print(f"\n优点:")
            for strength in result['detailed_analysis']['strengths']:
                print(f"  + {strength}")
            
            print(f"\n不足:")
            for weakness in result['detailed_analysis']['weaknesses']:
                print(f"  - {weakness}")
            
            print(f"\n建议:")
            for suggestion in result['detailed_analysis']['suggestions']:
                print(f"  → {suggestion}")
            
            # 保存详细分析结果
            output_file = f"ai_analysis_{student_name}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n详细分析结果已保存到: {output_file}")
        else:
            print(f"分析失败: 无法处理 {student_name} 的报告")

if __name__ == "__main__":
    main()
