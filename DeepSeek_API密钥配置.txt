
# DeepSeek API 密钥配置说明

## 获取API密钥
1. 访问 DeepSeek 官网: https://platform.deepseek.com/
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制API密钥

## 配置方法

### 方法1: 编辑配置文件（推荐）
编辑 deepseek_config.json 文件:
```json
{
  "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "base_url": "https://api.deepseek.com",
  "model": "deepseek-reasoner"
}
```

### 方法2: 设置环境变量
```bash
set DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
set DEEPSEEK_BASE_URL=https://api.deepseek.com
```

## 注意事项
- 请妥善保管您的API密钥
- 不要将API密钥提交到版本控制系统
- 定期检查API使用量和费用
