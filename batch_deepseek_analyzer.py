#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量DeepSeek分析系统
使用DeepSeek R1对所有学生报告进行批量分析
"""

import json
import os
import time
import csv
from datetime import datetime
from deepseek_ai_analyzer import DeepSeekAIAnalyzer
from deepseek_config import DeepSeekConfig

class BatchDeepSeekAnalyzer:
    def __init__(self):
        self.config_manager = DeepSeekConfig()
        self.config = self.config_manager.get_config()
        self.analyzer = DeepSeekAIAnalyzer(
            api_key=self.config['api_key'],
            base_url=self.config['base_url']
        )
        self.results = []
        self.report_folder = "实验0-1大模型质量我来测"
        
    def check_configuration(self):
        """检查配置是否完整"""
        if not self.config_manager.is_configured():
            print("❌ DeepSeek API未配置")
            print("请先运行: python deepseek_config.py")
            return False
        
        print("✅ DeepSeek API配置检查通过")
        print(f"  模型: {self.config['model']}")
        print(f"  API地址: {self.config['base_url']}")
        return True
    
    def load_student_list(self):
        """加载学生名单"""
        try:
            with open('students.json', 'r', encoding='utf-8') as f:
                students = json.load(f)
            print(f"✅ 成功加载 {len(students)} 名学生信息")
            return students
        except FileNotFoundError:
            print("❌ 未找到学生名单文件 students.json")
            print("请先运行成绩管理系统生成学生名单")
            return []
        except Exception as e:
            print(f"❌ 加载学生名单失败: {e}")
            return []
    
    def find_student_report(self, student_name):
        """查找学生报告文件"""
        if not os.path.exists(self.report_folder):
            return None
        
        # 搜索包含学生姓名的文件
        for root, dirs, files in os.walk(self.report_folder):
            for file in files:
                if student_name in file and file.lower().endswith(('.pdf', '.docx', '.doc')):
                    return os.path.join(root, file)
        return None
    
    def estimate_cost(self, students):
        """估算API调用成本"""
        print(f"\n💰 成本估算")
        print(f"="*40)
        
        # DeepSeek定价 (示例，请根据实际定价调整)
        # 假设每1000 tokens约0.002美元
        estimated_tokens_per_report = 10000  # 估算每个报告需要的token数
        cost_per_1k_tokens = 0.002  # 美元
        
        total_students = len(students)
        total_tokens = total_students * estimated_tokens_per_report
        estimated_cost = (total_tokens / 1000) * cost_per_1k_tokens
        
        print(f"预计分析学生数: {total_students}")
        print(f"预计总token数: {total_tokens:,}")
        print(f"预计成本: ${estimated_cost:.2f} USD")
        print(f"预计时间: {total_students * 30 / 60:.1f} 分钟")
        
        confirm = input(f"\n是否继续批量分析? (y/N): ").strip().lower()
        return confirm in ['y', 'yes']
    
    def analyze_all_reports(self, limit=None):
        """批量分析所有报告"""
        print("🚀 开始DeepSeek批量分析...")
        print("="*80)
        
        # 检查配置
        if not self.check_configuration():
            return
        
        # 加载学生名单
        students = self.load_student_list()
        if not students:
            return
        
        # 限制分析数量（用于测试）
        if limit:
            students = students[:limit]
            print(f"⚠️  限制分析数量: {limit} 名学生")
        
        # 成本估算
        if not self.estimate_cost(students):
            print("❌ 用户取消分析")
            return
        
        total_students = len(students)
        successful_analyses = 0
        failed_analyses = 0
        no_report_count = 0
        api_errors = 0
        
        start_time = time.time()
        
        for i, student in enumerate(students, 1):
            student_name = student['name']
            print(f"\n[{i}/{total_students}] 分析学生: {student_name}")
            
            # 查找报告文件
            report_file = self.find_student_report(student_name)
            if not report_file:
                print(f"  ❌ 未找到 {student_name} 的报告文件")
                no_report_count += 1
                
                self.results.append({
                    'student_name': student_name,
                    'class': student['class'],
                    'status': 'no_report',
                    'scores': None,
                    'total_score': 0,
                    'grade_level': '未提交',
                    'analysis_time': datetime.now().isoformat()
                })
                continue
            
            print(f"  📄 找到报告: {os.path.basename(report_file)}")
            
            try:
                # 执行DeepSeek分析
                analysis_result = self.analyzer.analyze_report_with_deepseek(student_name, report_file)
                
                if analysis_result:
                    # 添加班级信息
                    analysis_result['class'] = student['class']
                    analysis_result['status'] = 'analyzed'
                    
                    self.results.append(analysis_result)
                    successful_analyses += 1
                    
                    print(f"  ✅ DeepSeek分析完成 - 总分: {analysis_result['total_score']}/100 ({analysis_result['grade_level']})")
                    
                    # API调用间隔，避免频率限制
                    time.sleep(2)
                    
                else:
                    print(f"  ❌ DeepSeek分析失败")
                    failed_analyses += 1
                    api_errors += 1
                    
                    self.results.append({
                        'student_name': student_name,
                        'class': student['class'],
                        'status': 'analysis_failed',
                        'scores': None,
                        'total_score': -1,
                        'grade_level': 'API失败',
                        'analysis_time': datetime.now().isoformat()
                    })
                
            except Exception as e:
                print(f"  ❌ 分析异常: {e}")
                failed_analyses += 1
                
                self.results.append({
                    'student_name': student_name,
                    'class': student['class'],
                    'status': 'error',
                    'scores': None,
                    'total_score': -1,
                    'grade_level': '错误',
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                })
            
            # 显示进度
            elapsed_time = time.time() - start_time
            avg_time_per_student = elapsed_time / i
            remaining_students = total_students - i
            estimated_remaining_time = avg_time_per_student * remaining_students
            
            print(f"  ⏱️  已用时: {elapsed_time:.1f}s, 预计剩余: {estimated_remaining_time:.1f}s")
            
            # 如果API错误过多，询问是否继续
            if api_errors >= 3 and i < total_students:
                continue_analysis = input(f"\n⚠️  API错误较多({api_errors}次)，是否继续? (y/N): ").strip().lower()
                if continue_analysis not in ['y', 'yes']:
                    print("❌ 用户终止分析")
                    break
        
        # 显示总结
        total_time = time.time() - start_time
        print(f"\n{'='*80}")
        print(f"🎯 DeepSeek批量分析完成!")
        print(f"{'='*80}")
        print(f"总学生数: {total_students}")
        print(f"成功分析: {successful_analyses}")
        print(f"分析失败: {failed_analyses}")
        print(f"未提交报告: {no_report_count}")
        print(f"API错误: {api_errors}")
        print(f"总用时: {total_time:.1f}秒")
        print(f"平均每个学生: {total_time/total_students:.1f}秒")
    
    def generate_analysis_report(self):
        """生成分析报告"""
        if not self.results:
            print("❌ 没有分析结果可生成报告")
            return
        
        print(f"\n📊 生成DeepSeek分析报告...")
        
        # 统计分析
        successful_results = [r for r in self.results if r['status'] == 'analyzed']
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 生成详细JSON报告
        report = {
            'analysis_summary': {
                'total_students': len(self.results),
                'successful_analyses': len(successful_results),
                'failed_analyses': len([r for r in self.results if r['status'] in ['analysis_failed', 'error']]),
                'no_reports': len([r for r in self.results if r['status'] == 'no_report']),
                'analysis_time': datetime.now().isoformat(),
                'ai_model': 'deepseek-reasoner',
                'api_provider': 'DeepSeek'
            },
            'detailed_results': self.results
        }
        
        if successful_results:
            scores = [r['total_score'] for r in successful_results]
            
            # 各维度分析
            dimensions = ['originality', 'completeness', 'standardization', 'data_analysis', 'insight_depth']
            dimension_stats = {}
            
            for dim in dimensions:
                dim_scores = [r['scores'][dim] for r in successful_results if r['scores']]
                if dim_scores:
                    dimension_stats[dim] = {
                        'average': sum(dim_scores) / len(dim_scores),
                        'max': max(dim_scores),
                        'min': min(dim_scores)
                    }
            
            # 等级分布
            grade_distribution = {}
            for result in successful_results:
                grade = result['grade_level']
                grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
            
            report['score_statistics'] = {
                'average_score': sum(scores) / len(scores),
                'max_score': max(scores),
                'min_score': min(scores),
                'grade_distribution': grade_distribution,
                'dimension_averages': dimension_stats
            }
        
        # 保存详细报告
        report_file = f"deepseek_batch_analysis_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 详细分析报告已保存: {report_file}")
        
        # 生成CSV格式的成绩单
        self.generate_csv_report(timestamp)
        
        # 显示统计信息
        self.display_statistics(report)
    
    def generate_csv_report(self, timestamp):
        """生成CSV格式的成绩报告"""
        csv_file = f"deepseek_grades_report_{timestamp}.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '序号', '班级', '姓名', '状态', '原创性', '完整性', '规范性', 
                '数据分析', '洞察深度', '总分', '等级', 'AI模型', '分析时间',
                '主要优点', '主要不足', '改进建议', '总体评价'
            ])
            
            # 写入数据
            for i, result in enumerate(self.results, 1):
                if result['status'] == 'analyzed' and result['scores']:
                    scores = result['scores']
                    analysis = result.get('detailed_analysis', {})
                    
                    strengths = '; '.join(analysis.get('strengths', [])[:2])
                    weaknesses = '; '.join(analysis.get('weaknesses', [])[:2])
                    suggestions = '; '.join(analysis.get('suggestions', [])[:2])
                    overall_comment = result.get('overall_comment', '')[:100] + '...' if len(result.get('overall_comment', '')) > 100 else result.get('overall_comment', '')
                    
                    writer.writerow([
                        i, result['class'], result['student_name'], '已分析',
                        scores['originality'], scores['completeness'], scores['standardization'],
                        scores['data_analysis'], scores['insight_depth'],
                        result['total_score'], result['grade_level'], 
                        result.get('ai_model', 'deepseek-reasoner'),
                        result.get('analysis_time', ''),
                        strengths, weaknesses, suggestions, overall_comment
                    ])
                else:
                    writer.writerow([
                        i, result['class'], result['student_name'], result['status'],
                        '', '', '', '', '', result['total_score'], result['grade_level'],
                        '', result.get('analysis_time', ''), '', '', '', ''
                    ])
        
        print(f"✅ CSV成绩报告已保存: {csv_file}")
    
    def display_statistics(self, report):
        """显示统计信息"""
        stats = report['analysis_summary']
        
        print(f"\n📈 DeepSeek分析统计报告")
        print(f"{'='*60}")
        
        print(f"📊 基本统计:")
        print(f"  总学生数: {stats['total_students']}")
        print(f"  成功分析: {stats['successful_analyses']} ({stats['successful_analyses']/stats['total_students']*100:.1f}%)")
        print(f"  分析失败: {stats['failed_analyses']}")
        print(f"  未提交: {stats['no_reports']}")
        print(f"  AI模型: {stats['ai_model']}")
        
        if 'score_statistics' in report:
            scores = report['score_statistics']
            print(f"\n🎯 成绩统计:")
            print(f"  平均分: {scores['average_score']:.1f}/100")
            print(f"  最高分: {scores['max_score']}/100")
            print(f"  最低分: {scores['min_score']}/100")
            
            print(f"\n📊 等级分布:")
            for grade, count in scores['grade_distribution'].items():
                percentage = count / stats['successful_analyses'] * 100
                print(f"  {grade}: {count}人 ({percentage:.1f}%)")

def main():
    print("🤖 DeepSeek R1 批量报告分析系统")
    print("="*80)
    
    batch_analyzer = BatchDeepSeekAnalyzer()
    
    # 询问分析模式
    print("请选择分析模式:")
    print("1. 测试模式 (分析前5名学生)")
    print("2. 完整分析 (分析所有学生)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        print("🧪 测试模式：分析前5名学生")
        batch_analyzer.analyze_all_reports(limit=5)
    elif choice == '2':
        print("🚀 完整分析模式")
        batch_analyzer.analyze_all_reports()
    else:
        print("❌ 无效选择")
        return
    
    # 生成分析报告
    batch_analyzer.generate_analysis_report()
    
    print(f"\n🎉 DeepSeek批量分析完成！")

if __name__ == "__main__":
    main()
