#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健壮的文档读取器
解决PDF和DOCX文件读取失败的问题
"""

import sys
import os
import subprocess
import tempfile
from pathlib import Path

def clean_text(text):
    """清理文本，移除特殊字符和编码问题"""
    if not text:
        return ""
    
    # 移除不可打印字符，保留基本的空白字符
    cleaned = ''.join(char for char in text if char.isprintable() or char in '\n\r\t ')
    
    # 移除过多的空行
    lines = cleaned.split('\n')
    cleaned_lines = []
    empty_count = 0
    
    for line in lines:
        if line.strip():
            cleaned_lines.append(line)
            empty_count = 0
        else:
            empty_count += 1
            if empty_count <= 2:  # 最多保留2个连续空行
                cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def extract_pdf_robust(pdf_path):
    """健壮的PDF文本提取"""
    text_content = ""
    
    # 方法1: 使用PyPDF2
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text(page_text) + "\n"
                except Exception as e:
                    print(f"PyPDF2提取第{page_num + 1}页失败: {e}")
                    continue
        
        if text_content.strip():
            return text_content
    except Exception as e:
        print(f"PyPDF2方法失败: {e}")
    
    # 方法2: 使用pdfplumber
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content += f"\n=== 第{page_num + 1}页 ===\n"
                        text_content += clean_text(page_text) + "\n"
                except Exception as e:
                    print(f"pdfplumber提取第{page_num + 1}页失败: {e}")
                    continue
        
        if text_content.strip():
            return text_content
    except ImportError:
        print("pdfplumber未安装")
    except Exception as e:
        print(f"pdfplumber方法失败: {e}")
    
    # 方法3: 使用pdfminer
    try:
        from pdfminer.high_level import extract_text
        text_content = extract_text(pdf_path)
        if text_content and text_content.strip():
            return clean_text(text_content)
    except ImportError:
        print("pdfminer未安装")
    except Exception as e:
        print(f"pdfminer方法失败: {e}")
    
    # 方法4: 使用系统命令（如果可用）
    try:
        # 尝试使用pdftotext命令
        result = subprocess.run(['pdftotext', pdf_path, '-'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0 and result.stdout.strip():
            return clean_text(result.stdout)
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
        print(f"pdftotext命令失败: {e}")
    
    return ""

def extract_docx_robust(docx_path):
    """健壮的DOCX文本提取"""
    text_content = ""
    
    # 方法1: 使用python-docx
    try:
        from docx import Document
        doc = Document(docx_path)
        
        # 提取段落
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content += paragraph.text + "\n"
        
        # 提取表格
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    text_content += " | ".join(row_text) + "\n"
        
        if text_content.strip():
            return clean_text(text_content)
            
    except ImportError:
        print("python-docx未安装")
    except Exception as e:
        print(f"python-docx方法失败: {e}")
    
    # 方法2: 使用zipfile直接解析XML
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(docx_path, 'r') as docx:
            # 读取主文档
            try:
                content = docx.read('word/document.xml')
                root = ET.fromstring(content)
                
                # 提取所有文本节点
                text_parts = []
                for elem in root.iter():
                    if elem.text and elem.text.strip():
                        text_parts.append(elem.text.strip())
                
                if text_parts:
                    text_content = ' '.join(text_parts)
                    return clean_text(text_content)
                    
            except Exception as e:
                print(f"XML解析失败: {e}")
                
    except Exception as e:
        print(f"zipfile方法失败: {e}")
    
    # 方法3: 使用系统命令转换
    try:
        # 尝试使用pandoc转换
        result = subprocess.run(['pandoc', docx_path, '-t', 'plain'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0 and result.stdout.strip():
            return clean_text(result.stdout)
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
        print(f"pandoc命令失败: {e}")
    
    return ""

def extract_document_content(file_path):
    """统一的文档内容提取接口"""
    if not os.path.exists(file_path):
        return None, f"文件不存在: {file_path}"
    
    file_ext = os.path.splitext(file_path)[1].lower()
    
    try:
        if file_ext == '.pdf':
            content = extract_pdf_robust(file_path)
        elif file_ext in ['.docx', '.doc']:
            content = extract_docx_robust(file_path)
        else:
            return None, f"不支持的文件格式: {file_ext}"
        
        if content and content.strip():
            return content, "成功"
        else:
            return None, "文件存在但无法提取有效内容"
            
    except Exception as e:
        return None, f"提取过程异常: {str(e)}"

def main():
    if len(sys.argv) != 2:
        print("用法: python robust_document_reader.py <文档文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    print(f"正在读取文档: {file_path}")
    
    content, status = extract_document_content(file_path)
    
    if content:
        print(f"\n状态: {status}")
        print("="*50)
        print("文档内容:")
        print("="*50)
        print(content)
    else:
        print(f"读取失败: {status}")
        sys.exit(1)

if __name__ == "__main__":
    main()
