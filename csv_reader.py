#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件读取工具
用于读取学生信息CSV文件
"""

import csv
import sys
import os
from pathlib import Path

def read_csv_with_encoding(csv_path, encodings=['utf-8', 'gbk', 'gb2312', 'utf-8-sig']):
    """
    尝试使用不同编码读取CSV文件
    
    Args:
        csv_path (str): CSV文件路径
        encodings (list): 要尝试的编码列表
        
    Returns:
        list: CSV数据行列表
    """
    
    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            with open(csv_path, 'r', encoding=encoding, newline='') as file:
                # 检测CSV分隔符
                sample = file.read(1024)
                file.seek(0)
                
                # 尝试不同的分隔符
                delimiters = [',', ';', '\t', '|']
                delimiter = ','
                
                for delim in delimiters:
                    if sample.count(delim) > sample.count(delimiter):
                        delimiter = delim
                
                print(f"检测到分隔符: '{delimiter}'")
                
                csv_reader = csv.reader(file, delimiter=delimiter)
                rows = list(csv_reader)
                
                if rows and len(rows) > 0:
                    print(f"成功读取 {len(rows)} 行数据")
                    return rows, encoding, delimiter
                    
        except UnicodeDecodeError:
            print(f"编码 {encoding} 失败，尝试下一个...")
            continue
        except Exception as e:
            print(f"使用编码 {encoding} 时出错: {e}")
            continue
    
    return None, None, None

def display_csv_data(rows, encoding, delimiter):
    """
    显示CSV数据
    """
    if not rows:
        print("没有数据可显示")
        return
    
    print(f"\n使用编码: {encoding}")
    print(f"分隔符: '{delimiter}'")
    print(f"总行数: {len(rows)}")
    print("\n" + "="*80)
    print("CSV文件内容:")
    print("="*80)
    
    # 显示表头
    if rows:
        headers = rows[0]
        print(f"表头: {headers}")
        print("-" * 80)
        
        # 显示前几行数据
        for i, row in enumerate(rows[1:], 1):
            if i <= 20:  # 只显示前20行
                print(f"第{i:2d}行: {row}")
            elif i == 21:
                print("...")
                print(f"（省略中间数据，共{len(rows)-1}行学生数据）")
                break
        
        # 显示最后几行
        if len(rows) > 21:
            print("最后几行:")
            for i, row in enumerate(rows[-3:], len(rows)-3):
                print(f"第{i:2d}行: {row}")

def main():
    if len(sys.argv) != 2:
        print("用法: python csv_reader.py <CSV文件路径>")
        sys.exit(1)
    
    csv_path = sys.argv[1]
    
    if not os.path.exists(csv_path):
        print(f"文件不存在: {csv_path}")
        sys.exit(1)
    
    print(f"正在读取CSV文件: {csv_path}")
    
    rows, encoding, delimiter = read_csv_with_encoding(csv_path)
    
    if rows is None:
        print("无法读取CSV文件，请检查文件格式和编码")
        sys.exit(1)
    
    display_csv_data(rows, encoding, delimiter)

if __name__ == "__main__":
    main()
