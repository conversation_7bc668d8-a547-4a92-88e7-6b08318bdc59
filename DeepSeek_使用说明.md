# DeepSeek R1 实验报告AI分析系统使用说明

## 📋 系统概述

本系统集成了DeepSeek R1大语言模型，为实验报告提供专业的AI分析和评估服务。系统支持单个报告分析和批量分析两种模式。

## 🚀 快速开始

### 1. 配置DeepSeek API

首先需要配置您的DeepSeek API密钥：

```bash
# 方法1: 运行配置程序（推荐）
python deepseek_config.py

# 方法2: 设置环境变量
set DEEPSEEK_API_KEY=your_api_key_here
set DEEPSEEK_BASE_URL=https://api.deepseek.com
```

### 2. 测试单个报告分析

```bash
python deepseek_ai_analyzer.py "学生姓名" "报告文件路径"

# 示例
python deepseek_ai_analyzer.py "万宇峰" "实验0-1大模型质量我来测/软件2301-万宇峰-大语言模型质量我来测报告.pdf"
```

### 3. 批量分析所有报告

```bash
python batch_deepseek_analyzer.py
```

## 📁 文件说明

### 核心文件

- **`deepseek_ai_analyzer.py`** - DeepSeek单个报告分析器
- **`batch_deepseek_analyzer.py`** - 批量分析系统
- **`deepseek_config.py`** - API配置管理工具

### 配置文件

- **`deepseek_config.json`** - API配置文件（自动生成）
- **`students.json`** - 学生名单（由成绩管理系统生成）

### 输出文件

- **`deepseek_analysis_[学生姓名]_[时间戳].json`** - 单个学生详细分析
- **`deepseek_batch_analysis_[时间戳].json`** - 批量分析详细报告
- **`deepseek_grades_report_[时间戳].csv`** - 成绩汇总表

## 🔧 详细配置

### API配置选项

```json
{
  "api_key": "your_deepseek_api_key",
  "base_url": "https://api.deepseek.com",
  "model": "deepseek-reasoner",
  "max_tokens": 4000,
  "temperature": 0.3,
  "timeout": 60
}
```

### 支持的模型

- **deepseek-reasoner** - 推理模型（推荐用于分析）
- **deepseek-chat** - 对话模型

## 📊 分析维度

系统基于以下5个维度进行评估（每项20分，总分100分）：

1. **原创性 (Originality)** - 内容独创性、个人见解深度
2. **完整性 (Completeness)** - 实验步骤完整性、数据充分性
3. **规范性 (Standardization)** - 格式规范、引用标准、语言表达
4. **数据分析 (Data Analysis)** - 数据处理科学性、分析方法合理性
5. **洞察深度 (Insight Depth)** - 结论深刻性、问题理解程度

## 💰 成本估算

### DeepSeek定价参考
- 输入token: ~$0.14/1M tokens
- 输出token: ~$0.28/1M tokens

### 预估成本
- 每个报告约消耗: 8,000-12,000 tokens
- 单个报告成本: ~$0.002-0.004 USD
- 69名学生批量分析: ~$0.15-0.30 USD

*注：实际价格请参考DeepSeek官方最新定价*

## 🎯 使用流程

### 单个报告分析流程

1. **配置API** - 运行 `python deepseek_config.py`
2. **测试连接** - 系统自动测试API连接
3. **分析报告** - 运行分析命令
4. **查看结果** - 系统显示详细分析结果
5. **保存报告** - 自动保存JSON格式的详细分析

### 批量分析流程

1. **检查配置** - 系统自动检查API配置
2. **加载学生名单** - 从students.json加载学生信息
3. **成本估算** - 显示预计成本和时间
4. **确认分析** - 用户确认后开始批量分析
5. **进度跟踪** - 实时显示分析进度
6. **生成报告** - 自动生成多种格式的分析报告

## 📈 输出报告格式

### JSON详细报告
```json
{
  "student_name": "学生姓名",
  "total_score": 85,
  "grade_level": "良好",
  "scores": {
    "originality": 17,
    "completeness": 16,
    "standardization": 18,
    "data_analysis": 15,
    "insight_depth": 19
  },
  "detailed_analysis": {
    "strengths": ["优点1", "优点2", "优点3"],
    "weaknesses": ["不足1", "不足2", "不足3"],
    "suggestions": ["建议1", "建议2", "建议3"]
  },
  "overall_comment": "总体评价...",
  "ai_model": "deepseek-reasoner"
}
```

### CSV成绩汇总
包含所有学生的成绩、评价和建议，便于导入Excel进行进一步分析。

## ⚠️ 注意事项

### API使用限制
- **频率限制**: 系统自动添加2秒间隔避免频率限制
- **Token限制**: 单次请求最大4000 tokens
- **超时设置**: 默认60秒超时

### 错误处理
- **网络错误**: 自动重试机制
- **API错误**: 详细错误信息显示
- **格式错误**: 自动验证和修复

### 数据安全
- **本地处理**: 所有文档在本地处理
- **API传输**: 仅分析内容通过API传输
- **配置安全**: API密钥本地加密存储

## 🔍 故障排除

### 常见问题

**Q: API密钥无效**
```
A: 检查API密钥是否正确，运行 python deepseek_config.py 重新配置
```

**Q: 网络连接失败**
```
A: 检查网络连接和防火墙设置，确认API地址正确
```

**Q: 分析结果格式错误**
```
A: 系统会自动重试，如持续出现请检查模型设置
```

**Q: 文档读取失败**
```
A: 确保ultimate_document_reader.py正常工作，检查文档格式
```

## 📞 技术支持

如遇到问题，请检查：

1. **API配置** - 确认密钥和地址正确
2. **网络连接** - 确认可以访问DeepSeek API
3. **文档格式** - 确认PDF/DOCX文件完整
4. **系统依赖** - 确认所有Python包已安装

## 🚀 高级功能

### 自定义分析提示词
可以修改 `deepseek_ai_analyzer.py` 中的 `analysis_prompt_template` 来自定义分析标准。

### 批量分析限制
测试模式下可以限制分析数量，避免大量API调用：
```python
batch_analyzer.analyze_all_reports(limit=5)  # 只分析前5名学生
```

### 结果对比
可以使用现有的对比分析工具比较DeepSeek分析结果与传统评分的差异。

---

**系统版本**: v2.0  
**更新时间**: 2025年7月2日  
**支持模型**: DeepSeek R1 (deepseek-reasoner)
