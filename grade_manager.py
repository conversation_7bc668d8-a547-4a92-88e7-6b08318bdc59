#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验成绩管理系统
用于管理和记录学生的实验报告评分
"""

import csv
import json
import os
from datetime import datetime

class GradeManager:
    def __init__(self, student_csv_path, grade_json_path="grades.json"):
        self.student_csv_path = student_csv_path
        self.grade_json_path = grade_json_path
        self.students = []
        self.grades = {}
        
        self.load_students()
        self.load_grades()
    
    def load_students(self):
        """加载学生信息"""
        try:
            with open(self.student_csv_path, 'r', encoding='gbk', newline='') as file:
                csv_reader = csv.DictReader(file)
                self.students = list(csv_reader)
                print(f"成功加载 {len(self.students)} 名学生信息")
        except Exception as e:
            print(f"加载学生信息失败: {e}")
    
    def load_grades(self):
        """加载已有成绩"""
        if os.path.exists(self.grade_json_path):
            try:
                with open(self.grade_json_path, 'r', encoding='utf-8') as file:
                    self.grades = json.load(file)
                print(f"成功加载 {len(self.grades)} 条成绩记录")
            except Exception as e:
                print(f"加载成绩失败: {e}")
                self.grades = {}
        else:
            self.grades = {}
    
    def save_grades(self):
        """保存成绩到JSON文件"""
        try:
            with open(self.grade_json_path, 'w', encoding='utf-8') as file:
                json.dump(self.grades, file, ensure_ascii=False, indent=2)
            print(f"成绩已保存到 {self.grade_json_path}")
        except Exception as e:
            print(f"保存成绩失败: {e}")
    
    def add_grade(self, student_name, original_score, completeness_score, 
                  standard_score, analysis_score, insight_score, 
                  total_score, grade_level, comments):
        """添加学生成绩"""
        
        # 查找学生信息
        student_info = None
        for student in self.students:
            if student['姓名'] == student_name:
                student_info = student
                break
        
        if not student_info:
            print(f"未找到学生: {student_name}")
            return False
        
        grade_record = {
            "学号": student_info['学号'],
            "班级": student_info['班级'],
            "姓名": student_name,
            "原创性": original_score,
            "完整性": completeness_score,
            "规范性": standard_score,
            "数据分析": analysis_score,
            "洞察深度": insight_score,
            "综合得分": total_score,
            "评分等级": grade_level,
            "评分原因": comments,
            "评分时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.grades[student_name] = grade_record
        print(f"已添加 {student_name} 的成绩记录")
        return True
    
    def get_grade(self, student_name):
        """获取学生成绩"""
        return self.grades.get(student_name)
    
    def export_grades_csv(self, output_path="实验成绩汇总.csv"):
        """导出成绩到CSV文件"""
        if not self.grades:
            print("没有成绩数据可导出")
            return
        
        try:
            fieldnames = ["序号", "班级", "学号", "姓名", "原创性", "完整性", "规范性", 
                         "数据分析", "洞察深度", "综合得分", "评分等级", "评分原因", "评分时间"]
            
            with open(output_path, 'w', encoding='utf-8-sig', newline='') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, (name, grade) in enumerate(self.grades.items(), 1):
                    row = {"序号": i}
                    row.update(grade)
                    writer.writerow(row)
            
            print(f"成绩已导出到 {output_path}")
        except Exception as e:
            print(f"导出成绩失败: {e}")
    
    def display_grades(self):
        """显示所有成绩"""
        if not self.grades:
            print("暂无成绩记录")
            return
        
        print("\n" + "="*100)
        print("实验成绩汇总")
        print("="*100)
        print(f"{'序号':<4} {'班级':<10} {'姓名':<8} {'原创性':<6} {'完整性':<6} {'规范性':<6} {'数据分析':<8} {'洞察深度':<8} {'总分':<6} {'等级':<6}")
        print("-"*100)
        
        for i, (name, grade) in enumerate(self.grades.items(), 1):
            print(f"{i:<4} {grade['班级']:<10} {grade['姓名']:<8} {grade['原创性']:<6} {grade['完整性']:<6} {grade['规范性']:<6} {grade['数据分析']:<8} {grade['洞察深度']:<8} {grade['综合得分']:<6} {grade['评分等级']:<6}")

def main():
    # 初始化成绩管理器
    manager = GradeManager("实验成绩学生信息模板.csv")
    
    # 添加万宇峰同学的成绩（基于之前的评估）
    manager.add_grade(
        student_name="万宇峰",
        original_score=16,
        completeness_score=20,
        standard_score=18,
        analysis_score=17,
        insight_score=16,
        total_score=87,
        grade_level="良好",
        comments="原创性扣分：部分回答内容疑似直接复制模型输出，缺乏个人分析（-4分）；完整性满分：包含3个模型、12个提示词、4维度评分表、综合分析、结论建议；规范性扣分：个别错别字，部分图表缺少标题（-2分）；数据分析扣分：有详细数据但缺乏深度交叉分析（-3分）；洞察深度扣分：结论相对表面，改进建议不够具体（-4分）"
    )
    
    # 保存成绩
    manager.save_grades()
    
    # 显示成绩
    manager.display_grades()
    
    # 导出成绩
    manager.export_grades_csv()
    
    print(f"\n成绩管理完成！")
    print(f"- JSON格式成绩文件: {manager.grade_json_path}")
    print(f"- CSV格式成绩文件: 实验成绩汇总.csv")

if __name__ == "__main__":
    main()
