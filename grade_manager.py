#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验成绩管理系统
用于管理和记录学生的实验报告评分
"""

import csv
import json
import os
import re
import subprocess
from datetime import datetime
from pathlib import Path

class GradeManager:
    def __init__(self, student_csv_path, report_folder_path, grade_json_path="grades.json"):
        self.student_csv_path = student_csv_path
        self.report_folder_path = report_folder_path
        self.grade_json_path = grade_json_path
        self.students = []
        self.grades = {}

        self.load_students()
        self.load_grades()
    
    def load_students(self):
        """加载学生信息"""
        try:
            with open(self.student_csv_path, 'r', encoding='gbk', newline='') as file:
                csv_reader = csv.DictReader(file)
                self.students = list(csv_reader)
                print(f"成功加载 {len(self.students)} 名学生信息")
        except Exception as e:
            print(f"加载学生信息失败: {e}")
    
    def load_grades(self):
        """加载已有成绩"""
        if os.path.exists(self.grade_json_path):
            try:
                with open(self.grade_json_path, 'r', encoding='utf-8') as file:
                    self.grades = json.load(file)
                print(f"成功加载 {len(self.grades)} 条成绩记录")
            except Exception as e:
                print(f"加载成绩失败: {e}")
                self.grades = {}
        else:
            self.grades = {}
    
    def save_grades(self):
        """保存成绩到JSON文件"""
        try:
            with open(self.grade_json_path, 'w', encoding='utf-8') as file:
                json.dump(self.grades, file, ensure_ascii=False, indent=2)
            print(f"成绩已保存到 {self.grade_json_path}")
        except Exception as e:
            print(f"保存成绩失败: {e}")
    
    def add_grade(self, student_name, original_score, completeness_score, 
                  standard_score, analysis_score, insight_score, 
                  total_score, grade_level, comments):
        """添加学生成绩"""
        
        # 查找学生信息
        student_info = None
        for student in self.students:
            if student['姓名'] == student_name:
                student_info = student
                break
        
        if not student_info:
            print(f"未找到学生: {student_name}")
            return False
        
        grade_record = {
            "学号": student_info['学号'],
            "班级": student_info['班级'],
            "姓名": student_name,
            "原创性": original_score,
            "完整性": completeness_score,
            "规范性": standard_score,
            "数据分析": analysis_score,
            "洞察深度": insight_score,
            "综合得分": total_score,
            "评分等级": grade_level,
            "评分原因": comments,
            "评分时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.grades[student_name] = grade_record
        print(f"已添加 {student_name} 的成绩记录")
        return True
    
    def get_grade(self, student_name):
        """获取学生成绩"""
        return self.grades.get(student_name)
    
    def export_grades_csv(self, output_path="实验成绩汇总.csv"):
        """导出成绩到CSV文件"""
        if not self.grades:
            print("没有成绩数据可导出")
            return
        
        try:
            fieldnames = ["序号", "班级", "学号", "姓名", "原创性", "完整性", "规范性", 
                         "数据分析", "洞察深度", "综合得分", "评分等级", "评分原因", "评分时间"]
            
            with open(output_path, 'w', encoding='utf-8-sig', newline='') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                
                for i, (name, grade) in enumerate(self.grades.items(), 1):
                    row = {"序号": i}
                    row.update(grade)
                    writer.writerow(row)
            
            print(f"成绩已导出到 {output_path}")
        except Exception as e:
            print(f"导出成绩失败: {e}")
    
    def find_student_report(self, student_name):
        """查找学生的实验报告文件"""
        if not os.path.exists(self.report_folder_path):
            print(f"报告文件夹不存在: {self.report_folder_path}")
            return None

        # 获取所有文件
        all_files = []
        for root, dirs, files in os.walk(self.report_folder_path):
            for file in files:
                if file.lower().endswith(('.pdf', '.docx', '.doc')):
                    all_files.append(os.path.join(root, file))

        # 查找包含学生姓名的文件
        matching_files = []
        for file_path in all_files:
            file_name = os.path.basename(file_path)
            if student_name in file_name:
                matching_files.append(file_path)

        if not matching_files:
            print(f"未找到 {student_name} 的报告文件")
            return None

        # 如果有多个文件，优先选择包含"最新"、"final"等关键词的文件
        priority_keywords = ['最新', 'final', 'Final', '最终', 'v2', 'v3', '修改', '更新']

        for keyword in priority_keywords:
            for file_path in matching_files:
                if keyword in os.path.basename(file_path):
                    print(f"找到 {student_name} 的报告文件: {os.path.basename(file_path)}")
                    return file_path

        # 如果没有优先级文件，返回第一个匹配的文件
        selected_file = matching_files[0]
        print(f"找到 {student_name} 的报告文件: {os.path.basename(selected_file)}")
        return selected_file

    def extract_pdf_content(self, pdf_path):
        """提取PDF文件内容"""
        try:
            result = subprocess.run([
                'python', 'pdf_reader.py', pdf_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                # 提取实际内容部分
                output = result.stdout
                if "PDF内容提取成功:" in output:
                    content_start = output.find("PDF内容提取成功:") + len("PDF内容提取成功:")
                    content_start = output.find("="*50, content_start) + 50
                    return output[content_start:].strip()
                return output
            else:
                print(f"PDF提取失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"PDF提取异常: {e}")
            return None

    def extract_docx_content(self, docx_path):
        """提取DOCX文件内容"""
        try:
            result = subprocess.run([
                'python', 'docx_reader.py', docx_path
            ], capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                # 提取实际内容部分
                output = result.stdout
                if "DOCX内容提取成功:" in output:
                    content_start = output.find("DOCX内容提取成功:") + len("DOCX内容提取成功:")
                    content_start = output.find("="*50, content_start) + 50
                    return output[content_start:].strip()
                return output
            else:
                print(f"DOCX提取失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"DOCX提取异常: {e}")
            return None

    def evaluate_report_content(self, content, student_name):
        """基于报告内容进行评估"""
        if not content:
            return None

        # 简化的评估逻辑 - 基于内容特征进行评分
        original_score = 15  # 默认原创性分数
        completeness_score = 15  # 默认完整性分数
        standard_score = 15  # 默认规范性分数
        analysis_score = 15  # 默认数据分析分数
        insight_score = 15  # 默认洞察深度分数

        # 检查完整性要素
        required_elements = {
            '3个模型': ['deepseek', 'Deepseek', '通义千问', '文心一言', 'ChatGPT', 'GPT', 'Claude'],
            '12个提示词': ['提示词', '问题', '测试'],
            '4维度评分': ['维度', '评分', '分数'],
            '数据分析': ['分析', '对比', '结果'],
            '结论建议': ['结论', '建议', '总结']
        }

        completeness_count = 0
        for element, keywords in required_elements.items():
            if any(keyword in content for keyword in keywords):
                completeness_count += 1

        completeness_score = min(20, 12 + completeness_count * 1.6)

        # 检查原创性（简单检测重复内容）
        lines = content.split('\n')
        valid_lines = [l for l in lines if len(l.strip()) > 10]
        unique_lines = set(line.strip() for line in valid_lines)
        if len(valid_lines) > 0:
            uniqueness_ratio = len(unique_lines) / len(valid_lines)
            original_score = min(20, 10 + uniqueness_ratio * 10)
        else:
            uniqueness_ratio = 0.5

        # 检查规范性
        standard_score = 18  # 基础分数
        if '错别字' in content or len(re.findall(r'[？！。，]', content)) < 10:
            standard_score -= 2

        # 检查数据分析深度
        analysis_keywords = ['图表', '数据', '统计', '对比', '分析', '计算']
        analysis_count = sum(1 for keyword in analysis_keywords if keyword in content)
        analysis_score = min(20, 10 + analysis_count * 1.5)

        # 检查洞察深度
        insight_keywords = ['建议', '改进', '优化', '问题', '不足', '优势', '缺陷']
        insight_count = sum(1 for keyword in insight_keywords if keyword in content)
        insight_score = min(20, 10 + insight_count * 1.5)

        total_score = original_score + completeness_score + standard_score + analysis_score + insight_score

        # 确定等级
        if total_score >= 90:
            grade_level = "优秀"
        elif total_score >= 80:
            grade_level = "良好"
        elif total_score >= 70:
            grade_level = "中等"
        elif total_score >= 60:
            grade_level = "及格"
        else:
            grade_level = "不及格"

        return {
            'original_score': round(original_score),
            'completeness_score': round(completeness_score),
            'standard_score': round(standard_score),
            'analysis_score': round(analysis_score),
            'insight_score': round(insight_score),
            'total_score': round(total_score),
            'grade_level': grade_level,
            'comments': f"自动评估结果 - 完整性要素:{completeness_count}/5, 原创性比例:{uniqueness_ratio:.2f}, 分析关键词:{analysis_count}, 洞察关键词:{insight_count}"
        }

    def display_grades(self):
        """显示所有成绩"""
        if not self.grades:
            print("暂无成绩记录")
            return

        print("\n" + "="*100)
        print("实验成绩汇总")
        print("="*100)
        print(f"{'序号':<4} {'班级':<10} {'姓名':<8} {'原创性':<6} {'完整性':<6} {'规范性':<6} {'数据分析':<8} {'洞察深度':<8} {'总分':<6} {'等级':<6}")
        print("-"*100)

        for i, (name, grade) in enumerate(self.grades.items(), 1):
            print(f"{i:<4} {grade['班级']:<10} {grade['姓名']:<8} {grade['原创性']:<6} {grade['完整性']:<6} {grade['规范性']:<6} {grade['数据分析']:<8} {grade['洞察深度']:<8} {grade['综合得分']:<6} {grade['评分等级']:<6}")

    def process_all_students(self):
        """批量处理所有学生的报告"""
        print(f"\n开始批量处理 {len(self.students)} 名学生的报告...")

        processed_count = 0
        failed_count = 0

        for i, student in enumerate(self.students, 1):
            student_name = student['姓名']
            print(f"\n[{i}/{len(self.students)}] 处理学生: {student_name}")

            # 检查是否已经评估过
            if student_name in self.grades:
                print(f"  {student_name} 已有成绩记录，跳过")
                continue

            # 查找报告文件
            report_file = self.find_student_report(student_name)
            if not report_file:
                print(f"  未找到 {student_name} 的报告文件")
                failed_count += 1
                continue

            # 提取报告内容
            print(f"  正在提取报告内容...")
            if report_file.lower().endswith('.pdf'):
                content = self.extract_pdf_content(report_file)
            elif report_file.lower().endswith(('.docx', '.doc')):
                content = self.extract_docx_content(report_file)
            else:
                print(f"  暂不支持 {os.path.splitext(report_file)[1]} 格式")
                failed_count += 1
                continue

            if not content:
                print(f"  无法提取 {student_name} 的报告内容")
                failed_count += 1
                continue

            # 评估报告
            print(f"  正在评估报告...")
            evaluation = self.evaluate_report_content(content, student_name)
            if not evaluation:
                print(f"  评估 {student_name} 的报告失败")
                failed_count += 1
                continue

            # 添加成绩记录
            success = self.add_grade(
                student_name=student_name,
                original_score=evaluation['original_score'],
                completeness_score=evaluation['completeness_score'],
                standard_score=evaluation['standard_score'],
                analysis_score=evaluation['analysis_score'],
                insight_score=evaluation['insight_score'],
                total_score=evaluation['total_score'],
                grade_level=evaluation['grade_level'],
                comments=evaluation['comments']
            )

            if success:
                processed_count += 1
                print(f"  ✓ {student_name} 评估完成 - 总分: {evaluation['total_score']}, 等级: {evaluation['grade_level']}")
            else:
                failed_count += 1
                print(f"  ✗ {student_name} 成绩记录失败")

        print(f"\n批量处理完成!")
        print(f"成功处理: {processed_count} 名学生")
        print(f"处理失败: {failed_count} 名学生")

        # 保存成绩
        if processed_count > 0:
            self.save_grades()
            self.export_grades_csv()
            print(f"成绩已保存并导出到CSV文件")

def main():
    # 初始化成绩管理器
    manager = GradeManager(
        student_csv_path="实验成绩学生信息模板.csv",
        report_folder_path="实验0-1大模型质量我来测"
    )

    # 批量处理所有学生的报告
    manager.process_all_students()

    # 显示最终成绩
    manager.display_grades()

    print(f"\n批量评估完成！")
    print(f"- JSON格式成绩文件: {manager.grade_json_path}")
    print(f"- CSV格式成绩文件: 实验成绩汇总.csv")

if __name__ == "__main__":
    main()
